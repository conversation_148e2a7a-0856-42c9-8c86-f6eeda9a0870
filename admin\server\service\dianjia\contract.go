package dianjia

import (
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
)

type ContractService struct{}

// CreateContract 创建合同 - 重构后的新结构
func (contractService *ContractService) CreateContract(req dianjia.CreateContractRequest, setterID uint) error {
	// 检查合同编码是否唯一（在该setter_id下）
	var existingContract dianjia.Contract
	err := global.GVA_DB.Where("contract_code = ? AND setter_id = ?", req.ContractCode, setterID).First(&existingContract).Error
	if err == nil {
		return errors.New("合同编码已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 验证点价方是否存在
	var pricer system.SysUser
	err = global.GVA_DB.First(&pricer, req.PricerID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("点价方用户不存在")
		}
		return err
	}

	// 验证期货合约是否存在
	var instrument dianjia.Instrument
	err = global.GVA_DB.First(&instrument, req.InstrumentRefID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("指定的期货合约不存在")
		}
		return err
	}

	// 创建合同
	contract := dianjia.Contract{
		ContractCode:         req.ContractCode,
		SetterID:             setterID,
		PricerID:             req.PricerID,
		InstrumentRefID:      req.InstrumentRefID,
		Remarks:              req.Remarks,
		TotalQuantity:        req.TotalQuantity,
		RemainingQuantity:    req.TotalQuantity, // 初始时剩余数量等于总数量
		PriceType:            req.PriceType,
		PriceValue:           req.PriceValue,
		Status:               dianjia.ContractStatusUnexecuted, // V3: 手动创建的合同默认为未执行状态
		SourceTradeRequestID: nil,                              // 手动创建的合同没有源交易请求
		IsGenerated:          false,                            // 手动创建的合同不是系统生成的
	}

	return global.GVA_DB.Create(&contract).Error
}

// ValidateContractForOperation 验证合同是否可以进行业务操作 (V3)
func (contractService *ContractService) ValidateContractForOperation(contractID uint) error {
	var contract dianjia.Contract
	err := global.GVA_DB.First(&contract, contractID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在")
		}
		return err
	}

	// V3: 检查合同状态
	switch contract.Status {
	case dianjia.ContractStatusUnexecuted:
		return nil // 未执行状态可以操作（激活、取消、编辑、删除）
	case dianjia.ContractStatusExecuting:
		// 执行中状态只有在没有冻结数量时才能挂起
		if contract.FrozenQuantity > 0 {
			return errors.New("合同有正在进行的点价请求，无法操作")
		}
		return nil // 可以挂起
	case dianjia.ContractStatusCompleted:
		return errors.New("合同已完成，无法操作")
	case dianjia.ContractStatusCancelled:
		return errors.New("合同已取消，无法操作")
	default:
		return errors.New("合同状态异常，无法操作")
	}
}

// UpdateContract 更新合同（仅限Active状态）
func (contractService *ContractService) UpdateContract(req dianjia.UpdateContractRequest, setterID uint) error {
	var contract dianjia.Contract
	err := global.GVA_DB.Where("id = ? AND setter_id = ?", req.ID, setterID).First(&contract).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在或无权限")
		}
		return err
	}

	// V3: 检查合同状态：只有未执行状态的合同可以更新
	if contract.Status != dianjia.ContractStatusUnexecuted {
		return errors.New("只有未执行状态的合同可以更新")
	}

	// 检查是否有正在执行的交易请求
	var activeTradeRequestCount int64
	err = global.GVA_DB.Table("dj_execution_details").
		Joins("JOIN dj_trade_requests ON dj_execution_details.trade_request_id = dj_trade_requests.id").
		Where("dj_execution_details.contract_id = ? AND dj_trade_requests.status = ?",
			contract.ID, string(dianjia.TradeRequestStatusExecuting)).
		Count(&activeTradeRequestCount).Error
	if err != nil {
		return err
	}

	if activeTradeRequestCount > 0 {
		return errors.New("存在正在执行的交易请求，不能更新合同")
	}

	// 检查合同编码唯一性（排除当前合同，在该setter_id下）
	var existingContract dianjia.Contract
	err = global.GVA_DB.Where("contract_code = ? AND id != ? AND setter_id = ?", req.ContractCode, req.ID, setterID).First(&existingContract).Error
	if err == nil {
		return errors.New("合同编码已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 验证点价方是否存在
	var pricer system.SysUser
	err = global.GVA_DB.First(&pricer, req.PricerID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("点价方用户不存在")
		}
		return err
	}

	// 验证期货合约是否存在
	var instrument dianjia.Instrument
	err = global.GVA_DB.First(&instrument, req.InstrumentRefID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("指定的期货合约不存在")
		}
		return err
	}

	// 更新合同基本信息
	updates := map[string]interface{}{
		"contract_code":      req.ContractCode,
		"pricer_id":          req.PricerID,
		"instrument_ref_id":  req.InstrumentRefID,
		"remarks":            req.Remarks,
		"total_quantity":     req.TotalQuantity,
		"remaining_quantity": req.TotalQuantity, // 重置剩余数量
		"price_type":         req.PriceType,
		"price_value":        req.PriceValue,
	}

	return global.GVA_DB.Model(&contract).Updates(updates).Error
}

// DeleteContract 软删除合同
func (contractService *ContractService) DeleteContract(contractID uint, setterID uint) error {
	var contract dianjia.Contract
	err := global.GVA_DB.Where("id = ? AND setter_id = ?", contractID, setterID).First(&contract).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在或无权限")
		}
		return err
	}

	// V3: 检查合同状态：只有未执行状态的合同可以删除
	if contract.Status != dianjia.ContractStatusUnexecuted {
		return errors.New("只有未执行状态的合同可以删除")
	}

	return global.GVA_DB.Delete(&contract).Error
}

// GetContractDetail 获取合同详情 - 重构后
func (contractService *ContractService) GetContractDetail(contractID uint) (*dianjia.ContractResponse, error) {
	var contract dianjia.Contract

	err := global.GVA_DB.Where("id = ?", contractID).
		Preload("Setter").
		Preload("Pricer").
		Preload("Instrument").
		Preload("ExecutionDetails.TradeRequest").
		First(&contract).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	response := &dianjia.ContractResponse{
		Contract: contract,
	}

	return response, nil
}

// GetContractDetailWithPermission 获取合同详情（带权限验证）
func (contractService *ContractService) GetContractDetailWithPermission(contractID uint, userID uint) (*dianjia.ContractResponse, error) {
	var contract dianjia.Contract

	err := global.GVA_DB.Where("id = ?", contractID).
		Preload("Setter").
		Preload("Pricer").
		Preload("Instrument").
		Preload("ExecutionDetails.TradeRequest").
		First(&contract).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	// 权限验证：只有合同的setter或pricer可以查看详情
	if contract.SetterID != userID && contract.PricerID != userID {
		return nil, errors.New("无权限查看该合同详情")
	}

	response := &dianjia.ContractResponse{
		Contract: contract,
	}

	return response, nil
}

// GetContractsByType 根据价格类型获取合同 - 新增方法用于交易请求
func (contractService *ContractService) GetContractsByType(instrumentRefID uint, priceType dianjia.ContractPriceType) ([]dianjia.Contract, error) {
	var contracts []dianjia.Contract

	// V3: 查询条件：执行中状态、有剩余数量、且可用数量大于0（剩余数量-冻结数量）
	err := global.GVA_DB.Where("instrument_ref_id = ? AND price_type = ? AND status = ? AND remaining_quantity > frozen_quantity",
		instrumentRefID, priceType, dianjia.ContractStatusExecuting).
		Preload("Setter").
		Preload("Pricer").
		Preload("Instrument").
		Order("created_at ASC").
		Find(&contracts).Error

	return contracts, err
}

// GetContractSummaryByUserAndInstrument 获取用户在特定合约下的合同汇总 - 新增方法用于前端分组显示
func (contractService *ContractService) GetContractSummaryByUserAndInstrument(userID uint, userRole string) (map[uint]map[uint]map[string]int, error) {
	// 返回结构: map[setterID][instrumentID][priceType] = quantity
	summary := make(map[uint]map[uint]map[string]int)

	var contracts []dianjia.Contract
	query := global.GVA_DB.Model(&dianjia.Contract{}).Where("status = ?", dianjia.ContractStatusExecuting)

	if userRole == "setter" {
		query = query.Where("setter_id = ?", userID)
	} else if userRole == "pricer" {
		query = query.Where("pricer_id = ?", userID)
	}

	err := query.Preload("Setter").Preload("Instrument").Find(&contracts).Error
	if err != nil {
		return nil, err
	}

	for _, contract := range contracts {
		setterID := contract.SetterID
		instrumentID := contract.InstrumentRefID
		priceType := string(contract.PriceType)

		// 计算可用数量（剩余数量 - 冻结数量）
		availableQuantity := contract.RemainingQuantity - contract.FrozenQuantity
		if availableQuantity <= 0 {
			continue // 跳过没有可用数量的合同
		}

		if summary[setterID] == nil {
			summary[setterID] = make(map[uint]map[string]int)
		}
		if summary[setterID][instrumentID] == nil {
			summary[setterID][instrumentID] = make(map[string]int)
		}

		summary[setterID][instrumentID][priceType] += availableQuantity
	}

	return summary, nil
}

// GetContractsAsSetter 获取当前用户作为被点价方的合同列表 (V3) - 支持分页
func (contractService *ContractService) GetContractsAsSetter(req dianjia.ContractAsSetterRequest, userID uint) ([]dianjia.ContractResponse, int64, error) {
	var contracts []dianjia.Contract
	var total int64

	query := global.GVA_DB.Model(&dianjia.Contract{}).Where("setter_id = ?", userID)

	// V3: 状态过滤
	if req.Status != "" {
		// 支持新的状态值
		if req.Status == string(dianjia.ContractStatusUnexecuted) {
			query = query.Where("status = ?", dianjia.ContractStatusUnexecuted)
		} else if req.Status == string(dianjia.ContractStatusExecuting) {
			query = query.Where("status = ?", dianjia.ContractStatusExecuting)
		} else if req.Status == string(dianjia.ContractStatusCompleted) {
			query = query.Where("status = ?", dianjia.ContractStatusCompleted)
		} else if req.Status == string(dianjia.ContractStatusCancelled) {
			query = query.Where("status = ?", dianjia.ContractStatusCancelled)
		} else if req.Status == "Executing,Completed" {
			query = query.Where("status IN (?)", []dianjia.ContractStatus{dianjia.ContractStatusExecuting, dianjia.ContractStatusCompleted})
		}
	}
	// V3: 不设置默认状态过滤，显示所有状态的合同

	// 时间范围过滤
	if req.StartDate != "" {
		query = query.Where("DATE(created_at) >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("DATE(created_at) <= ?", req.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 设置分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize

	// 分页查询并预加载关联数据
	err := query.Preload("Setter").Preload("Pricer").Preload("Instrument").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&contracts).Error
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dianjia.ContractResponse, 0)
	for _, contract := range contracts {
		response := dianjia.ContractResponse{
			Contract: contract,
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// GetContractsAsPricer 获取当前用户作为点价方的合同列表 (V3) - 支持分页
func (contractService *ContractService) GetContractsAsPricer(req dianjia.ContractAsPricerRequest, userID uint) ([]dianjia.ContractResponse, int64, error) {
	var contracts []dianjia.Contract
	var total int64

	query := global.GVA_DB.Model(&dianjia.Contract{}).Where("pricer_id = ?", userID)

	// V3: 状态过滤
	if req.Status != "" {
		// 支持新的状态值
		if req.Status == string(dianjia.ContractStatusUnexecuted) {
			query = query.Where("status = ?", dianjia.ContractStatusUnexecuted)
		} else if req.Status == string(dianjia.ContractStatusExecuting) {
			query = query.Where("status = ?", dianjia.ContractStatusExecuting)
		} else if req.Status == string(dianjia.ContractStatusCompleted) {
			query = query.Where("status = ?", dianjia.ContractStatusCompleted)
		} else if req.Status == string(dianjia.ContractStatusCancelled) {
			query = query.Where("status = ?", dianjia.ContractStatusCancelled)
		} else if req.Status == "Executing,Completed" {
			query = query.Where("status IN (?)", []dianjia.ContractStatus{dianjia.ContractStatusExecuting, dianjia.ContractStatusCompleted})
		}
	}
	// V3: 不设置默认状态过滤，显示所有状态的合同

	// 时间范围过滤
	if req.StartDate != "" {
		query = query.Where("DATE(created_at) >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("DATE(created_at) <= ?", req.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 设置分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize

	// 分页查询并预加载关联数据
	err := query.Preload("Setter").Preload("Pricer").Preload("Instrument").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&contracts).Error
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dianjia.ContractResponse, 0)
	for _, contract := range contracts {
		response := dianjia.ContractResponse{
			Contract: contract,
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// UpdateContractQuantity 更新合同数量 - 用于交易执行时扣减剩余数量
func (contractService *ContractService) UpdateContractQuantity(contractID uint, executedQuantity int) error {
	var contract dianjia.Contract
	err := global.GVA_DB.First(&contract, contractID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在")
		}
		return err
	}

	// 检查剩余数量是否足够
	if contract.RemainingQuantity < executedQuantity {
		return errors.New("合同剩余数量不足")
	}

	// 扣减剩余数量
	contract.RemainingQuantity -= executedQuantity

	// 如果剩余数量为0，自动设置为已完成状态
	if contract.RemainingQuantity == 0 {
		contract.Status = dianjia.ContractStatusCompleted
	}

	return global.GVA_DB.Save(&contract).Error
}

// FreezeContractQuantity 冻结合同数量（系统内部调用）
func (contractService *ContractService) FreezeContractQuantity(contractID uint, quantity int, tradeRequestID uint) error {
	var contract dianjia.Contract
	err := global.GVA_DB.First(&contract, contractID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在")
		}
		return err
	}

	// V3: 检查合同状态
	if contract.Status != dianjia.ContractStatusExecuting {
		return errors.New("只有执行中状态的合同可以冻结")
	}

	// 检查可用数量是否足够
	availableQuantity := contract.RemainingQuantity - contract.FrozenQuantity
	if availableQuantity < quantity {
		return errors.New("合同可用数量不足")
	}

	// 冻结数量
	contract.FrozenQuantity += quantity
	return global.GVA_DB.Save(&contract).Error
}

// UnfreezeContractQuantity 解冻合同数量（系统内部调用）
func (contractService *ContractService) UnfreezeContractQuantity(contractID uint, quantity int, tradeRequestID uint, consumeQuantity int) error {
	var contract dianjia.Contract
	err := global.GVA_DB.First(&contract, contractID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在")
		}
		return err
	}

	// 检查冻结数量是否足够
	if contract.FrozenQuantity < quantity {
		return errors.New("合同冻结数量不足")
	}

	// 解冻数量
	contract.FrozenQuantity -= quantity

	// 如果有消耗数量，同时扣减剩余数量
	if consumeQuantity > 0 {
		if contract.RemainingQuantity < consumeQuantity {
			return errors.New("合同剩余数量不足")
		}
		contract.RemainingQuantity -= consumeQuantity

		// 如果剩余数量为0，自动设置为已完成状态
		if contract.RemainingQuantity == 0 {
			contract.Status = dianjia.ContractStatusCompleted
		}
	}

	return global.GVA_DB.Save(&contract).Error
}

// AutoFreezeContractsForTradeRequest 自动冻结合同数量（点价请求时调用）
func (contractService *ContractService) AutoFreezeContractsForTradeRequest(tx *gorm.DB, instrumentRefID uint, priceType dianjia.ContractPriceType, requestedQuantity int, tradeRequestID uint) ([]uint, error) {
	// V3: 获取所有可用的合同，按创建时间排序
	var contracts []dianjia.Contract
	err := tx.Where("instrument_ref_id = ? AND price_type = ? AND status = ?",
		instrumentRefID, priceType, dianjia.ContractStatusExecuting).
		Order("created_at ASC").
		Find(&contracts).Error
	if err != nil {
		return nil, err
	}

	var frozenContractIDs []uint
	remainingQuantity := requestedQuantity

	for _, contract := range contracts {
		if remainingQuantity <= 0 {
			break
		}

		// 计算可用数量
		availableQuantity := contract.RemainingQuantity - contract.FrozenQuantity
		if availableQuantity <= 0 {
			continue
		}

		// 计算需要冻结的数量
		freezeQuantity := availableQuantity
		if freezeQuantity > remainingQuantity {
			freezeQuantity = remainingQuantity
		}

		// 冻结数量（使用传入的事务）
		err = tx.Model(&contract).Update("frozen_quantity", contract.FrozenQuantity+freezeQuantity).Error
		if err != nil {
			return nil, err
		}

		frozenContractIDs = append(frozenContractIDs, contract.ID)
		remainingQuantity -= freezeQuantity
	}

	// 检查是否满足了全部需求
	if remainingQuantity > 0 {
		return nil, errors.New("可用合同数量不足")
	}

	return frozenContractIDs, nil
}

// GetContractCancelRecords 获取合同取消记录
func (contractService *ContractService) GetContractCancelRecords(contractID uint, userID uint) ([]dianjia.ContractCancelRecord, error) {
	// 首先验证用户是否有权限查看该合同
	var contract dianjia.Contract
	err := global.GVA_DB.Where("id = ?", contractID).First(&contract).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	// 权限验证：只有合同的setter或pricer可以查看取消记录
	if contract.SetterID != userID && contract.PricerID != userID {
		return nil, errors.New("无权限查看该合同的取消记录")
	}

	// 查询取消记录
	var records []dianjia.ContractCancelRecord
	err = global.GVA_DB.Where("contract_id = ?", contractID).
		Preload("User").
		Order("created_at DESC").
		Find(&records).Error

	if err != nil {
		return nil, err
	}

	return records, nil
}

// ActivateContract 激活合同 (V3 新增)
func (contractService *ContractService) ActivateContract(contractID uint, setterID uint) error {
	var contract dianjia.Contract
	err := global.GVA_DB.Where("id = ? AND setter_id = ?", contractID, setterID).First(&contract).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在或无权限")
		}
		return err
	}

	// 检查合同状态
	if contract.Status != dianjia.ContractStatusUnexecuted {
		return errors.New("只有未执行状态的合同可以激活")
	}

	// 更新状态为执行中
	contract.Status = dianjia.ContractStatusExecuting
	return global.GVA_DB.Save(&contract).Error
}

// DeactivateContract 挂起合同 (V3 新增)
func (contractService *ContractService) DeactivateContract(contractID uint, setterID uint) error {
	var contract dianjia.Contract
	err := global.GVA_DB.Where("id = ? AND setter_id = ?", contractID, setterID).First(&contract).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在或无权限")
		}
		return err
	}

	// 检查合同状态
	if contract.Status != dianjia.ContractStatusExecuting {
		return errors.New("只有执行中状态的合同可以挂起")
	}

	// 检查是否有冻结数量
	if contract.FrozenQuantity > 0 {
		return errors.New("合同有正在进行的点价请求，无法挂起")
	}

	// 更新状态为未执行
	contract.Status = dianjia.ContractStatusUnexecuted
	return global.GVA_DB.Save(&contract).Error
}

// CancelContract 取消合同 (V3 新增)
func (contractService *ContractService) CancelContract(contractID uint, cancelQuantity int, setterID uint) error {
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var contract dianjia.Contract
	err := tx.Where("id = ? AND setter_id = ?", contractID, setterID).First(&contract).Error
	if err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在或无权限")
		}
		return err
	}

	// 检查合同状态：只有未执行状态的合同可以取消
	if contract.Status != dianjia.ContractStatusUnexecuted {
		tx.Rollback()
		return errors.New("只有未执行状态的合同可以取消")
	}

	// 检查取消数量是否合理
	if cancelQuantity <= 0 {
		tx.Rollback()
		return errors.New("取消数量必须大于0")
	}
	if cancelQuantity > contract.RemainingQuantity {
		tx.Rollback()
		return errors.New("取消数量不能超过剩余数量")
	}

	// 扣减数量
	contract.TotalQuantity -= cancelQuantity
	contract.RemainingQuantity -= cancelQuantity

	// 如果剩余数量为0，状态变为已取消
	if contract.RemainingQuantity == 0 {
		contract.Status = dianjia.ContractStatusCancelled
	}
	// 否则状态保持为未执行

	err = tx.Save(&contract).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
