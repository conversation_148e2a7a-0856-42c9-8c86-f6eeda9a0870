<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { useMarketStore } from '@/store/market'

defineOptions({
  name: 'BasisPriceDisplay'
})

// Props
interface Props {
  instrumentId: string
  exchangeId: string
  price: number
  showPrice?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPrice: false
})

// ==================== 期货价格相关状态 ====================

// 获取market store实例
const marketStore = useMarketStore()

// 期货价格状态管理
const futuresPrice = ref<number | null>(null)
const fetchingPrice = ref(false)
const priceError = ref(false)

// 动画状态
const showFlash = ref(false)

// 获取期货价格的函数，使用market store
function getFuturesPrice(instrumentId: string): number | null {
  try {
    const marketData = marketStore.getMarketData(instrumentId)
    if (marketData && marketData.last_price) {
      const price = parseFloat(marketData.last_price)
      return isNaN(price) ? null : price
    }
    return null
  } catch (error) {
    console.error('获取期货价格失败:', error)
    return null
  }
}

// 订阅和获取期货价格
function subscribeAndGetPrice() {
  if (!props.instrumentId || !props.exchangeId) {
    priceError.value = true
    fetchingPrice.value = false
    return
  }

  try {
    // 检查是否已经有数据
    const existingPrice = getFuturesPrice(props.instrumentId)
    if (existingPrice !== null) {
      futuresPrice.value = existingPrice
      priceError.value = false
      fetchingPrice.value = false
      return
    }

    // 如果没有数据，则订阅行情
    fetchingPrice.value = true
    priceError.value = false

    marketStore.subscribe(props.instrumentId, props.exchangeId)
    
    // 设置超时检查，如果5秒后还没有数据则显示错误
    setTimeout(() => {
      if (fetchingPrice.value) {
        const price = getFuturesPrice(props.instrumentId)
        if (price !== null) {
          futuresPrice.value = price
          priceError.value = false
        } else {
          priceError.value = true
        }
        fetchingPrice.value = false
      }
    }, 5000)
    
  } catch (error) {
    console.error('订阅期货价格失败:', error)
    fetchingPrice.value = false
    priceError.value = true
  }
}

// 监听showPrice和合约变化，自动获取期货价格
watch(
  () => [props.showPrice, props.instrumentId],
  ([showPrice, instrumentId]) => {
    // 触发闪烁动画
    showFlash.value = true
    setTimeout(() => {
      showFlash.value = false
    }, 600)

    if (showPrice && instrumentId) {
      subscribeAndGetPrice()
    } else {
      // 重置状态
      futuresPrice.value = null
      fetchingPrice.value = false
      priceError.value = false
    }
  },
  { immediate: true }
)

// 监听市场数据变化，实时更新期货价格
watch(
  () => marketStore.marketData,
  (marketData) => {
    if (props.showPrice && props.instrumentId) {
      const price = getFuturesPrice(props.instrumentId)
      if (price !== null) {
        futuresPrice.value = price
        priceError.value = false
        fetchingPrice.value = false
      }
    }
  },
  { deep: true }
)

// 计算最终价格
const finalPrice = computed(() => {
  if (props.showPrice && futuresPrice.value !== null) {
    return futuresPrice.value + props.price
  }
  return null
})

// ==================== 格式化和工具函数 ====================

/**
 * 智能数字格式化函数
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 */
function formatLargeNumber(num: number): string {
  const absNum = Math.abs(num)
  const sign = num < 0 ? '-' : ''

  // 小于万的数字直接显示
  if (absNum < 10000) {
    return sign + absNum.toLocaleString()
  }

  // 万级别显示
  if (absNum < 100000000) { // 1亿以下
    const wan = absNum / 10000
    if (wan >= 100) {
      return sign + Math.round(wan) + '万'
    } else {
      return sign + wan.toFixed(1) + '万'
    }
  }

  // 亿级别显示
  const yi = absNum / 100000000
  if (yi >= 100) {
    return sign + Math.round(yi) + '亿'
  } else {
    return sign + yi.toFixed(1) + '亿'
  }
}

/**
 * 格式化基差价格，确保显示正负符号
 * @param price 基差价格
 * @returns 带正负符号的格式化价格
 */
function formatBasisPrice(price: number): string {
  const formatted = formatLargeNumber(Math.abs(price))
  return price >= 0 ? `+${formatted}` : `-${formatted}`
}

/**
 * 动态字体大小计算
 * @param text 文本内容
 * @param baseSize 基础字体大小
 * @param minSize 最小字体大小
 * @returns 计算后的字体大小
 */
function calculateDynamicFontSize(text: string, baseSize: number = 48, minSize: number = 28): number {
  const textLength = text.length

  // 根据文字长度动态调整字体大小
  if (textLength <= 6) return baseSize
  if (textLength <= 8) return Math.max(baseSize * 0.9, minSize)
  if (textLength <= 10) return Math.max(baseSize * 0.8, minSize)
  return minSize
}
</script>

<template>
  <view class="basis-display" :class="{ 'flash-bg': showFlash }">
    <transition name="price-switch" mode="out-in">
      <template v-if="!showPrice">
        <!-- 原始基差显示：合约 + 基差 -->
        <view key="basis" class="basis-content">
          <text class="contract-name">{{ instrumentId }}</text>
          <text
            class="basis-value adaptive-price"
            :style="{
              fontSize: calculateDynamicFontSize(formatBasisPrice(price), 42, 26) + 'rpx'
            }"
            :data-positive="price >= 0"
            :data-negative="price < 0"
            :title="(price >= 0 ? '+' : '') + price.toLocaleString()"
          >
            {{ formatBasisPrice(price) }}
          </text>
        </view>
      </template>
      <template v-else>
        <!-- 最终价格显示：期货价格 + 基差 = 最终价格 -->
        <view key="final" class="final-content">
          <template v-if="fetchingPrice">
            <text class="loading-text">获取价格中...</text>
          </template>
          <template v-else-if="priceError || finalPrice === null">
            <text class="error-text">未获取到期货价格</text>
          </template>
          <template v-else>
            <text class="final-price-label">最终价格</text>
            <text
              class="final-price-value adaptive-price"
              :style="{
                fontSize: calculateDynamicFontSize(formatLargeNumber(finalPrice), 42, 26) + 'rpx'
              }"
              :title="finalPrice.toLocaleString()"
            >
              {{ formatLargeNumber(finalPrice) }}
            </text>
          </template>
        </view>
      </template>
    </transition>
  </view>
</template>

<style lang="scss" scoped>
// 设计系统变量
$primary-color: #667eea;

.basis-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  min-width: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;

  // 闪烁背景效果
  &.flash-bg {
    animation: flashBackground 0.6s ease;
  }

  .basis-content,
  .final-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 6rpx;
    width: 100%;
    min-height: 80rpx;
    position: relative;
  }

  .contract-name {
    font-size: 26rpx;
    color: #595959;
    font-weight: 700;
    letter-spacing: 2rpx;
    text-transform: uppercase;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
    text-align: center;
  }

  .basis-value {
    font-weight: 900;
    line-height: 1.0;
    text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.15);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
    text-align: center;
    transition: font-size 0.3s ease;

    // 根据正负号显示不同颜色
    &:first-letter {
      font-size: 36rpx;
    }

    // 正数显示红色（涨）
    &[data-positive="true"] {
      color: #ff4d4f;
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    // 负数显示绿色（跌）
    &[data-negative="true"] {
      color: #52c41a;
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .loading-text {
    font-size: 24rpx;
    color: #999;
    text-align: center;
    line-height: 1.2;
  }

  .error-text {
    font-size: 22rpx;
    color: #ff4d4f;
    text-align: center;
    line-height: 1.2;
    opacity: 0.8;
  }

  .final-price-label {
    font-size: 22rpx;
    color: #666;
    text-align: center;
    margin-bottom: 4rpx;
    font-weight: 500;
  }

  .final-price-value {
    font-weight: 900;
    line-height: 1.0;
    text-align: center;
    color: #1890ff;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.15);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
    transition: font-size 0.3s ease;
  }
}

// 自适应价格显示通用样式
.adaptive-price {
  // 确保价格文字在容器内完整显示
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  // 添加hover效果显示完整价格
  &:hover {
    position: relative;
    z-index: 100;

    &::after {
      content: attr(title);
      position: absolute;
      top: -40rpx;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8rpx 12rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      white-space: nowrap;
      z-index: 101;
      pointer-events: none;
      opacity: 0;
      animation: fadeIn 0.3s ease forwards;
    }
  }
}

// 滑动切换动画
.price-switch-enter-active,
.price-switch-leave-active {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// 新内容从右侧滑入
.price-switch-enter-from {
  opacity: 0.8;
  transform: translateX(120%) scale(0.95);
}

.price-switch-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
}

// 旧内容向左侧滑出
.price-switch-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.price-switch-leave-to {
  opacity: 0.8;
  transform: translateX(-120%) scale(0.95);
}

.price-switch-enter-active {
  z-index: 2;
}

.price-switch-leave-active {
  z-index: 1;
}

// 背景闪烁动画
@keyframes flashBackground {
  0% {
    background-color: transparent;
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
  15% {
    background-color: rgba(102, 126, 234, 0.1);
    box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  }
  30% {
    background-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.15);
  }
  45% {
    background-color: rgba(102, 126, 234, 0.25);
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.2);
  }
  60% {
    background-color: rgba(102, 126, 234, 0.15);
    box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  }
  80% {
    background-color: rgba(102, 126, 234, 0.05);
    box-shadow: 0 0 0 2rpx rgba(102, 126, 234, 0.05);
  }
  100% {
    background-color: transparent;
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 响应式字体大小
@media (max-width: 750rpx) {
  .adaptive-price {
    // 小屏幕下进一步缩小字体
    &.basis-value {
      font-size: clamp(22rpx, 4.5vw, 36rpx) !important;
    }

    &.final-price-value {
      font-size: clamp(22rpx, 4.5vw, 36rpx) !important;
    }
  }
}
</style>