<template>
  <view class="page-container gradient-bg-primary">
    <!-- 页面标题 - 吸顶操作栏 -->
    <view class="page-header sticky-header">
      <text class="page-title">合同详情</text>
      <view class="header-actions">
        <!-- Setter操作按钮 -->
        <template v-if="contractDetail && isSetter">
          <!-- 激活按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contractDetail.status === 'Unexecuted'"
            type="success"
            size="small"
            @click="activateContract"
          >
            激活
          </wd-button>

          <!-- 挂起按钮 - 仅在 Executing 状态且无冻结数量时显示 -->
          <wd-button
            v-if="contractDetail.status === 'Executing' && contractDetail.frozenQuantity === 0"
            type="warning"
            size="small"
            @click="deactivateContract"
          >
            挂起
          </wd-button>

          <!-- 取消按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contractDetail.status === 'Unexecuted'"
            type="error"
            size="small"
            @click="cancelContract"
          >
            取消
          </wd-button>

          <!-- 编辑按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contractDetail.status === 'Unexecuted'"
            type="primary"
            size="small"
            @click="editContract"
          >
            编辑
          </wd-button>
        </template>

        <!-- 通用操作按钮 -->
        <wd-button
          v-if="contractDetail && hasCancelRecords"
          type="info"
          size="small"
          @click="viewCancelRecords"
        >
          取消记录
        </wd-button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <wd-loading />
      <text>加载中...</text>
    </view>

    <!-- 合同详情 -->
    <view v-else-if="contractDetail" class="contract-detail">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-title">基本信息</view>
        <view class="info-card common-card">
          <view class="info-row">
            <text class="label">合同编码:</text>
            <text class="value">{{ contractDetail.contractCode }}</text>
          </view>
          <view class="info-row">
            <text class="label">期货合约:</text>
            <text class="value">
              {{ contractDetail.instrument ? 
                `${contractDetail.instrument.instrument_name} (${contractDetail.instrument.instrument_id})` : 
                contractDetail.instrumentRefID 
              }}
            </text>
          </view>
          <view v-if="contractDetail.instrument" class="info-row">
            <text class="label">交易所:</text>
            <text class="value">{{ getExchangeName(contractDetail.instrument.exchange_id) }}</text>
          </view>
          <view v-if="contractDetail.instrument" class="info-row">
            <text class="label">品种:</text>
            <text class="value">{{ contractDetail.instrument.product_name }}</text>
          </view>
          <view class="info-row">
            <text class="label">状态:</text>
            <wd-tag :type="getStatusType(contractDetail.status)">
              {{ getStatusText(contractDetail.status) }}
            </wd-tag>
          </view>
          <view class="info-row">
            <text class="label">创建时间:</text>
            <text class="value">{{ formatDateTime(contractDetail.CreatedAt) }}</text>
          </view>
          <view class="info-row">
            <text class="label">更新时间:</text>
            <text class="value">{{ formatDateTime(contractDetail.UpdatedAt) }}</text>
          </view>
          <view class="info-row">
            <text class="label">备注:</text>
            <text class="value">{{ contractDetail.remarks || '无' }}</text>
          </view>
        </view>
      </view>

      <!-- 合同条款 -->
      <view class="detail-section">
        <view class="section-title">合同条款</view>
        <view class="info-card common-card">
          <view class="info-row">
            <text class="label">合同类型:</text>
            <text class="value">{{ getContractTypeText(contractDetail.priceType) }}</text>
          </view>
          <view class="info-row">
            <text class="label">合同总数量:</text>
            <text class="value">{{ contractDetail.totalQuantity }} 手</text>
          </view>
          <view class="info-row">
            <text class="label">剩余数量:</text>
            <text class="value">{{ contractDetail.remainingQuantity }} 手</text>
          </view>
          <view v-if="contractDetail.frozenQuantity > 0" class="info-row">
            <text class="label">冻结数量:</text>
            <text class="value frozen-text">{{ contractDetail.frozenQuantity }} 手</text>
          </view>
          <view v-if="contractDetail.frozenQuantity > 0" class="info-row">
            <text class="label">可用数量:</text>
            <text class="value">{{ contractDetail.remainingQuantity - contractDetail.frozenQuantity }} 手</text>
          </view>
          <view class="info-row">
            <text class="label">价格/基差:</text>
            <text class="value">{{ contractDetail.priceValue }}</text>
          </view>
          <view class="info-row">
            <text class="label">执行进度:</text>
            <text class="value">{{ ((contractDetail.totalQuantity - contractDetail.remainingQuantity) / contractDetail.totalQuantity * 100).toFixed(1) }}%</text>
          </view>
        </view>
      </view>

      <!-- 点价方信息 -->
      <view class="detail-section">
        <view class="section-title">点价方信息</view>
        <view class="info-card common-card">
          <view v-if="contractDetail.pricer" class="user-info">
            <view class="user-basic">
              <text class="user-name">{{ contractDetail.pricer.nickName || contractDetail.pricer.userName }}</text>
              <text class="user-phone">{{ contractDetail.pricer.phone }}</text>
            </view>
          </view>
          <view v-else class="empty-user">
            <text>未指定点价方</text>
          </view>
        </view>
      </view>

      <!-- 被点价方信息 -->
      <view class="detail-section">
        <view class="section-title">被点价方信息</view>
        <view class="info-card common-card">
          <view v-if="contractDetail.setter" class="user-info">
            <view class="user-basic">
              <text class="user-name">{{ contractDetail.setter.nickName || contractDetail.setter.userName }}</text>
              <text class="user-phone">{{ contractDetail.setter.phone }}</text>
            </view>
          </view>
          <view v-else class="empty-user">
            <text>未指定被点价方</text>
          </view>
        </view>
      </view>

      <!-- 执行明细 -->
      <view class="detail-section">
        <view class="section-title">执行明细</view>
        <view class="info-card common-card">
          <view v-if="contractDetail.executionDetails && contractDetail.executionDetails.length > 0" class="execution-list">
            <view v-for="execution in contractDetail.executionDetails" :key="execution.ID" class="execution-item">
              <view class="execution-header">
                <text class="execution-id">执行ID: {{ execution.ID }}</text>
                <wd-tag :type="execution.status === 'Success' ? 'success' : 'danger'">
                  {{ execution.status === 'Success' ? '成功' : '失败' }}
                </wd-tag>
              </view>
              <view class="execution-details">
                <view class="info-row">
                  <text class="label">执行数量:</text>
                  <text class="value">{{ execution.executedQuantity }} 手</text>
                </view>
                <view class="info-row">
                  <text class="label">执行价格:</text>
                  <text class="value">{{ execution.executedPrice }}</text>
                </view>
                <view class="info-row">
                  <text class="label">合同价格:</text>
                  <text class="value">{{ execution.contractPrice }}</text>
                </view>
                <view class="info-row">
                  <text class="label">结果价格:</text>
                  <text class="value">{{ execution.resultPrice }}</text>
                </view>
                <view class="info-row">
                  <text class="label">执行类型:</text>
                  <text class="value">{{ execution.executionType === 'Online' ? '线上' : '线下' }}</text>
                </view>
                <view v-if="execution.remarks" class="info-row">
                  <text class="label">备注:</text>
                  <text class="value">{{ execution.remarks }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-executions">
            <text>暂无执行明细</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-state">
      <text>获取合同详情失败</text>
      <wd-button type="primary" @click="loadContractDetail">重试</wd-button>
    </view>
  </view>

  <!-- 取消合同弹窗 -->
  <CancelContractDialog
    v-model="showCancelDialog"
    :contract-data="contractDetail"
    @confirm="handleCancelConfirm"
  />
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "合同详情"
  }
}
</route>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { navigateToPage } from '@/utils/index'
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
import {
  getContractDetail,
  activateContract as activateContractAPI,
  deactivateContract as deactivateContractAPI,
  cancelContract as cancelContractAPI
} from '@/api/contract'
import type { IContractResponse, UserRole, ContractStatus, ContractPriceType, ICancelContractRequest } from '@/types/contract'
import { ExchangeMap } from '@/types/instrument'
import CancelContractDialog from '@/components/CancelContractDialog.vue'


// 响应式数据
const contractDetail = ref<IContractResponse | null>(null)
const loading = ref(false)
const contractId = ref<number>(0)
const userRole = ref<UserRole>('setter')

// 取消弹窗相关
const showCancelDialog = ref(false)

// 计算属性
const isSetter = computed(() => {
  if (!contractDetail.value) return false
  // 这里需要根据实际的用户信息判断，暂时用合同的setter信息
  // 实际应该从用户store或者API获取当前用户信息
  return true // 暂时返回true，实际需要判断当前用户是否为setter
})

const hasCancelRecords = computed(() => {
  if (!contractDetail.value) return false
  return contractDetail.value.status === 'Cancelled' ||
         contractDetail.value.remainingQuantity < contractDetail.value.totalQuantity
})

// 方法
function loadContractDetail() {
  if (!contractId.value) {
    uni.showToast({
      title: '合同ID不能为空',
      icon: 'error',
    })
    return
  }

  loading.value = true

  getContractDetail(contractId.value)
    .then(response => {
      if (response.code === 0) {
        contractDetail.value = response.data
        console.log('合同详情:', contractDetail.value)
      } else {
        uni.showToast({
          title: response.msg || '获取合同详情失败',
          icon: 'error',
        })
      }
    })
    .catch(error => {
      console.error('获取合同详情失败:', error)
      uni.showToast({
        title: '网络错误',
        icon: 'error',
      })
    })
    .finally(() => {
      loading.value = false
    })
}

function getContractTypeText(priceType: ContractPriceType): string {
  const typeMap = {
    basis: '基差合同（点价）',
    fixed: '固定价合同（洗基差）'
  }
  return typeMap[priceType] || priceType
}

// 获取状态类型 - 更新为 V3 状态
function getStatusType(status: ContractStatus): TagType {
  const statusMap: Record<ContractStatus, TagType> = {
    Unexecuted: 'warning',
    Executing: 'success',
    Pending: 'warning',
    Completed: 'primary',
    Cancelled: 'danger',
  }
  return statusMap[status] || ('warning' as TagType)
}

function getStatusText(status: ContractStatus) {
  const statusMap = {
    Unexecuted: '未执行',
    Executing: '执行中',
    Pending: '待处理',
    Completed: '已完成',
    Cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

function formatDateTime(dateStr: string) {
  if (!dateStr) return '未设置'
  
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

function getExchangeName(exchangeId: string) {
  return ExchangeMap[exchangeId] || exchangeId
}

// 激活合同
async function activateContract() {
  if (!contractId.value) return

  try {
    const response = await activateContractAPI(contractId.value)
    if (response.code === 0) {
      uni.showToast({
        title: '激活成功',
        icon: 'success',
      })
      loadContractDetail()
    } else {
      uni.showToast({
        title: response.msg || '激活失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('激活合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

// 挂起合同
async function deactivateContract() {
  if (!contractId.value) return

  try {
    const response = await deactivateContractAPI(contractId.value)
    if (response.code === 0) {
      uni.showToast({
        title: '挂起成功',
        icon: 'success',
      })
      loadContractDetail()
    } else {
      uni.showToast({
        title: response.msg || '挂起失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('挂起合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

// 编辑合同
function editContract() {
  navigateToPage({
    url: `/pages/contract/form?id=${contractId.value}&mode=edit`
  })
}

// 取消合同
function cancelContract() {
  showCancelDialog.value = true
}

// 处理取消确认
async function handleCancelConfirm(data: { cancelQuantity: number; reason: string }) {
  if (!contractDetail.value) return

  try {
    const cancelData: ICancelContractRequest = {
      cancelQuantity: data.cancelQuantity,
      reason: data.reason
    }

    const response = await cancelContractAPI(contractDetail.value.ID, cancelData)

    if (response.code === 0) {
      uni.showToast({
        title: '取消成功',
        icon: 'success',
      })
      showCancelDialog.value = false
      // 重新加载合同详情
      loadContractDetail()
    } else {
      uni.showToast({
        title: response.msg || '取消失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('取消合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

// 查看取消记录
function viewCancelRecords() {
  navigateToPage({
    url: `/pages/contract/cancel-records?contractId=${contractId.value}`
  })
}

// 生命周期
// 使用 onLoad 获取页面参数
onLoad((options) => {
  // 从页面参数获取合同ID和用户角色
  contractId.value = Number(options?.id)
  userRole.value = (options?.role as UserRole) || 'setter'
  
  if (contractId.value) {
    loadContractDetail()
  } else {
    uni.showToast({
      title: '合同ID不能为空',
      icon: 'error',
    })
  }
})
</script>

<style lang="scss" scoped>
// 页面容器样式已移至全局样式文件

// 吸顶头部样式
.page-header {
  position: sticky;
  z-index: 100;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .header-actions {
    display: flex;
    gap: 20rpx;
    align-items: center;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  
  text {
    margin-top: 20rpx;
    font-size: 28rpx;
  }
}

.contract-detail {
  padding: 0 20rpx 20rpx 20rpx; // 为内容区域添加padding

  .detail-section {
    margin-bottom: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .info-card {
      padding: 30rpx;
      
      .info-row {
        display: flex;
        margin-bottom: 20rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          flex-shrink: 0;
          width: 160rpx;
          font-size: 28rpx;
          color: #999;
        }
        
        .value {
          flex: 1;
          font-size: 28rpx;
          color: #333;

          &.generated-tag {
            color: #ff9500;
            font-weight: bold;
          }

          &.pending-notice {
            color: #ff6b35;
            font-style: italic;
          }

          &.frozen-text {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
      
      .trade-rules-table {
        width: 100%;
        font-size: 28rpx;
        color: #333;
        border: 1rpx solid #ebeef5;
        border-radius: 8rpx;
        overflow: hidden;
        
        .table-header {
          display: flex;
          background-color: #f5f7fa;
          font-weight: bold;
          
          .table-cell {
            flex: 1;
            padding: 16rpx 10rpx;
            text-align: center;
            border-right: 1rpx solid #ebeef5;
            
            &:last-child {
              border-right: none;
            }
          }
          
          .property-cell {
            width: 180rpx;
            flex: none;
            background-color: #e6f3ff;
          }
        }
        
        .table-row {
          display: flex;
          border-top: 1rpx solid #ebeef5;
          
          &:nth-child(even) {
            background-color: #fafafa;
          }
          
          .table-cell {
            flex: 1;
            padding: 16rpx 10rpx;
            text-align: center;
            border-right: 1rpx solid #ebeef5;
            
            &:last-child {
              border-right: none;
            }
          }
          
          .property-cell {
            width: 180rpx;
            flex: none;
            background-color: #f5f7fa;
            font-weight: 500;
            text-align: left;
            padding-left: 20rpx;
          }
        }
      }
      
      .empty-table {
        text-align: center;
        padding: 40rpx;
        color: #999;
        font-size: 28rpx;
        border: 1rpx dashed #ebeef5;
        border-radius: 8rpx;
      }
      
      .user-info {
        .user-basic {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .user-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
          }
          
          .user-phone {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
      
      .empty-user,
      .empty-executions {
        text-align: center;
        padding: 40rpx;
        color: #999;
        font-size: 28rpx;
      }
      
      .execution-list {
        .execution-item {
          padding: 20rpx;
          border-bottom: 1rpx solid #f0f0f0;
          margin-bottom: 20rpx;
          
          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }
          
          .execution-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;
            
            .execution-id {
              font-size: 28rpx;
              font-weight: 500;
              color: #333;
            }
          }
          
          .execution-details {
            padding-left: 20rpx;
          }
        }
      }
    }
  }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 40rpx;
}
</style>