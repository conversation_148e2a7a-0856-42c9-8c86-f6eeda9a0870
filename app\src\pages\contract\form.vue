<script lang="ts" setup>
import type {
  ICreateContractRequest,
  IUpdateContractRequest,
  ContractPriceType,
} from '@/types/contract'
import type { IInstrumentSelectItem } from '@/types/instrument'
import type { IUser } from '@/types/user'
import { onMounted, reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  createContract,
  getContractDetail,
  updateContract,
} from '@/api/contract'
import InstrumentSelector from '@/components/InstrumentSelector.vue'
import UserSelector from '@/components/UserSelector.vue'
import { navigateBackOrTo } from '@/utils'


// 响应式数据
const formRef = ref()
const isEdit = ref(false)
const contractId = ref<number>(0)
const submitLoading = ref(false)

// 表单数据
const formData = reactive<ICreateContractRequest>({
  contractCode: '',
  pricerID: 0,
  instrumentRefID: 0,
  instrument: null,
  remarks: '',
  totalQuantity: 0,
  priceType: 'basis',
  priceValue: 0,
})

// 用户选择相关
const selectedPricer = ref<IUser | null>(null)

// 表单验证规则
const formRules = reactive({
  contractCode: [
    { required: true, message: '请输入合同编码' },
  ],
  totalQuantity: [
    { required: true, message: '请输入合同总数量', trigger: 'blur' },
    { pattern: /^(0|[1-9]\d*)$/ as RegExp, message: '数量必须为非负整数', trigger: 'blur' },
  ],
  priceValue: [
    { required: true, message: '请输入价格/基差值', trigger: 'blur' },
  ],
} as any)

// 方法
async function loadContractDetail() {
  if (!contractId.value)
    return

  try {
    const response = await getContractDetail(contractId.value)

    if (response.code === 0) {
      const contract = response.data

      // 检查合同状态，只有 Unexecuted 状态的合同才能编辑
      if (contract.status !== 'Unexecuted') {
        uni.showToast({
          title: '只有未执行状态的合同才能编辑',
          icon: 'error',
        })
        setTimeout(() => {
          navigateBackOrTo('/pages/contract/setter-list');
        }, 1500)
        return
      }

      // 确保 priceType 是字符串类型
      let priceType: 'basis' | 'fixed' = 'basis'
      if (typeof contract.priceType === 'string') {
        priceType = contract.priceType as 'basis' | 'fixed'
      } else if (typeof contract.priceType === 'object' && contract.priceType && 'value' in contract.priceType) {
        // 如果 priceType 是对象，尝试提取 value 属性
        priceType = (contract.priceType as any).value as 'basis' | 'fixed'
      }

      // 填充表单数据
      Object.assign(formData, {
        contractCode: contract.contractCode,
        pricerID: contract.pricerID,
        instrumentRefID: contract.instrumentRefID,
        instrument: contract.instrument || null,
        remarks: contract.remarks,
        totalQuantity: contract.totalQuantity,
        priceType: priceType,
        priceValue: contract.priceValue,
      })

      selectedPricer.value = contract.pricer

      // 如果有点价方信息，触发 UserSelector 组件的用户变化事件
      if (contract.pricer) {
        onUserChange(contract.pricer)
      }
    }
    else {
      uni.showToast({
        title: response.msg || '获取合同详情失败',
        icon: 'error',
      })
    }
  }
  catch (error) {
    console.error('获取合同详情失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

function onPriceTypeChange(value: any) {
  // 正确提取字符串值
  let newPriceType: 'basis' | 'fixed'

  if (typeof value === 'string') {
    newPriceType = value as 'basis' | 'fixed'
  } else if (typeof value === 'object' && value && 'value' in value) {
    newPriceType = value.value as 'basis' | 'fixed'
  } else {
    newPriceType = 'basis' // 默认值
  }

  formData.priceType = newPriceType
}

// 处理用户选择变化
function onUserChange(user: IUser | null) {
  selectedPricer.value = user
  formData.pricerID = user ? user.ID : 0
}

// 处理期货合约选择变化
function onInstrumentChange(selectedInstrumentId: number, instrumentData?: IInstrumentSelectItem | null) {
  formData.instrumentRefID = selectedInstrumentId || 0
  formData.instrument = instrumentData || null
}

async function submitForm() {
  // 表单验证
  const valid = await formRef.value.validate()
  if (!valid.valid)
    return

  if (!formData.instrumentRefID ) {
    uni.showToast({
      title: '请选择期货合约',
      icon: 'error',
    })
    return
  }

  if (!formData.totalQuantity || formData.totalQuantity <= 0) {
    uni.showToast({
      title: '请输入有效的合同数量',
      icon: 'error',
    })
    return
  }

  if (!formData.priceValue || formData.priceValue === 0) {
    uni.showToast({
      title: '请输入有效的价格/基差值',
      icon: 'error',
    })
    return
  }

  if (!formData.pricerID) {
    uni.showToast({
      title: '请选择点价方',
      icon: 'error',
    })
    return
  }

  submitLoading.value = true

  try {
    let response: IResData<any>

    // 准备提交数据，排除前端辅助字段
    const { instrument, ...submitData } = formData

    if (isEdit.value) {
      const updateData: IUpdateContractRequest = {
        id: contractId.value,
        ...submitData,
      }
      response = await updateContract(updateData)
    }
    else {
      // 新创建的合同状态默认为 Unexecuted（由后端处理）
      response = await createContract(submitData)
    }

    if (response.code === 0) {
      uni.showToast({
        title: isEdit.value ? '更新成功' : '创建成功',
        icon: 'success',
      })

      setTimeout(() => {
        navigateBackOrTo('/pages/contract/setter-list')
      }, 1500)
    }
    else {
      uni.showToast({
        title: response.msg || (isEdit.value ? '更新失败' : '创建失败'),
        icon: 'error',
      })
    }
  }
  catch (error) {
    console.error('提交合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
  finally {
    submitLoading.value = false
  }
}

function goBack() {
  navigateBackOrTo('/pages/contract/setter-list')
}

// 生命周期
// 使用 onLoad 获取页面参数
onLoad((options) => {
  // 检查是否为编辑模式
  const id = options?.id
  if (id) {
    contractId.value = Number(id)
    isEdit.value = true
    loadContractDetail()
  }
})
</script>

<template>
  <view class="page-container gradient-bg-primary">
    <!-- 表单内容 -->
    <view class="form-container">
      <wd-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 合同信息 -->
        <wd-cell-group title="合同信息" border custom-class="form-section">
          <!-- 1. 合同编码 -->
          <wd-input
            v-model="formData.contractCode"
            label="合同编码"
            label-width="160rpx"
            placeholder="请输入合同编码"
            required
            prop="contractCode"
            clearable
          />
          
          <!-- 2. 点价方 -->
          <UserSelector
            v-model="formData.pricerID"
            label="选择点价方"
            label-width="160rpx"
            placeholder="暂未选择点价方"
            required
            @change="onUserChange"
          />
          
          <!-- 3. 期货合约 -->
          <InstrumentSelector
            v-model="formData.instrumentRefID"
            label="期货合约"
            label-width="160rpx"
            placeholder="请输入期货合约"
            required
            @change="onInstrumentChange"
          />
          
          <!-- 4. 价格类型 -->
          <wd-cell title="价格类型" label-width="160rpx" required>
            <wd-radio-group
              v-model="formData.priceType"
              shape="button"
              inline
              custom-class="dj-price-type-radio-group"
              @change="onPriceTypeChange"
            >
              <wd-radio value="basis">基差合同</wd-radio>
              <wd-radio value="fixed">固定价</wd-radio>
            </wd-radio-group>
          </wd-cell>
          
          <!-- 5. 价格|基差值 -->
          <wd-input
            v-model.number="formData.priceValue"
            :label="formData.priceType === 'basis' ? '基差值' : '固定价格'"
            label-width="160rpx"
            type="number"
            :placeholder="formData.priceType === 'basis' ? '请输入基差值' : '请输入固定价格'"
            prop="priceValue"
            required
            clearable
          />
          
          <!-- 6. 数量 -->
          <wd-input
            v-model.number="formData.totalQuantity"
            label="合同总数量"
            label-width="160rpx"
            type="number"
            placeholder="请输入合同总数量"
            prop="totalQuantity"
            required
            clearable
          />
          
          <!-- 7. 备注 -->
          <wd-textarea
            v-model="formData.remarks"
            label="备注"
            label-width="160rpx"
            placeholder="请输入备注信息"
            :maxlength="500"
            show-word-limit
            :auto-height="true"
          />
        </wd-cell-group>
      </wd-form>
    </view>

    <!-- 底部操作按钮 -->
    <view class="form-actions">
      <wd-button type="info" custom-class="dj-btn-secondary" @click="goBack">
        取消
      </wd-button>
      <wd-button type="primary" custom-class="dj-btn-primary" :loading="submitLoading" @click="submitForm">
        {{ isEdit ? '更新合同' : '创建合同' }}
      </wd-button>
    </view>


  </view>
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "合同表单"
  }
}
</route>

<style lang="scss" scoped>
// 基础变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$secondary-color: #764ba2;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$border-color: #e0e3ea;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$box-shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$transition-base: all 0.3s ease;
$font-size-large: 32rpx;
$font-size-medium: 28rpx;
$font-size-small: 24rpx;
$font-size-title: 36rpx;
$font-size-page-title: 40rpx;

// 页面容器样式已移至全局样式文件
// 页面基本布局调整
.page-container {
  padding-bottom: 120rpx;
  padding-top: 20rpx;
}

.form-container {
  padding: 20rpx;
}

// 表单分区
.form-section {
  margin-bottom: 20rpx;

  .section-title {
    font-size: $font-size-title;
    font-weight: bold;
    color: $text-primary;
    margin-bottom: 30rpx;
    padding-bottom: 16rpx;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 80rpx;
      height: 4rpx;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      border-radius: 2rpx;
    }
  }
}

// 底部操作按钮区
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: white;
  display: flex;
  gap: 20rpx;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  :deep(.wd-button) {
    flex: 1;
  }
}



// 统一表单组件样式
:deep() {
  // 表单字段容器基本样式
  .dj-form-field,
  .dj-form-textarea {
    background: white;
    border-radius: 8rpx;
    transition: $transition-base;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: #f9fafc;
    }

    &:focus-within {
      background-color: #f9fafc;
      box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
    }

    &.is-disabled {
      background-color: #f5f7fa;
      cursor: not-allowed;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4rpx;
      background: linear-gradient(180deg, $primary-color, $secondary-color);
    }
  }

  // 表单标签
  .dj-form-label {
    color: $text-primary;
    text-align: right;
    justify-content: flex-end;
    width: 200rpx;
    padding-right: 24rpx;
    font-size: $font-size-medium;
  }

  // 表单输入
  .dj-form-input {
    color: $text-primary;
    font-size: $font-size-medium;

    &.is-placeholder {
      color: $text-light;
      font-style: italic;
    }

    &:focus {
      color: $primary-color;
    }
  }

  // 文本区容器
  .dj-form-textarea-container {
    width: 100%;
  }

  // 按钮样式基础
  %button-base {
    border-radius: 8rpx;
    font-weight: 500;
    font-size: $font-size-large;
    transition: $transition-base;
    padding: 20rpx;

    &:hover {
      opacity: 0.9;
    }

    &:active {
      opacity: 1;
    }
  }

  // 主按钮
  .dj-btn-primary {
    @extend %button-base;
    background: $primary-gradient;
    border: none;
    color: white;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
      opacity: 0.95;
    }

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
      opacity: 1;
    }

    &.is-loading {
      background: linear-gradient(135deg, #a5b0f3 0%, #a586c0 100%);
      opacity: 0.8;
    }
  }

  // 次按钮
  .dj-btn-secondary {
    @extend %button-base;
    background: white;
    color: $text-secondary;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

    &:hover {
      color: $text-primary;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    }

    &:active {
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    }
  }

  // 危险按钮
  .dj-btn-danger {
    @extend %button-base;
    background: linear-gradient(135deg, #f56c6c 0%, #e64242 100%);
    border: none;
    color: white;
    box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.3);
      opacity: 0.95;
    }

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);
      opacity: 1;
    }
  }

  // 价格类型选择器样式
  .dj-price-type-radio-group {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;

    .wd-radio {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 0;
    }
  }

  // 复选框和单选框的共同样式
  %checkbox-radio-common {
    .wd-checkbox__label,
    .wd-radio__label {
      color: $text-primary;
      font-size: $font-size-medium;
      font-weight: 500;
      padding-left: 12rpx;
    }

    .wd-checkbox__shape,
    .wd-radio__shape {
      border-color: #c0c4cc;
      transition: $transition-base;

      &.is-checked {
        background-color: $primary-color;
        border-color: $primary-color;
        box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
      }
    }
  }

  // 复选框
  .dj-checkbox {
    @extend %checkbox-radio-common;

    .wd-checkbox__shape {
      border-radius: 4rpx;
    }
  }

  // 单选框
  .dj-radio {
    @extend %checkbox-radio-common;
  }



  // 搜索框
  .dj-search {
    background: white;
    border-radius: 40rpx;
    padding: 0 16rpx;
    box-shadow: $box-shadow-sm;
    transition: $transition-base;

    &:focus-within {
      box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
    }

    .wd-search__input {
      background: transparent;
      color: $text-primary;
      font-size: $font-size-medium;
      height: 80rpx;
    }

    .wd-search__icon {
      color: $primary-color;
    }

    .wd-search__placeholder {
      color: $text-light;
      font-size: $font-size-medium;
    }
  }

  // 弹窗
  .dj-popup {
    .wd-popup__container {
      border-radius: 16rpx 16rpx 0 0;
      background: #ffffff;
      box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }
  }
}
</style>
