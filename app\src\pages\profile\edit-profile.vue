<route lang="json">
{
  "style": {
    "navigationBarTitleText": "修改资料"
  }
}
</route>

<template>
  <view class="page-container">
    <!-- 个人资料列表 -->
    <view class="profile-list common-card">
      <view class="section-title">个人资料</view>
      
      <!-- 昵称 -->
      <view class="list-item" @click="editFieldHandler('nickName')">
        <view class="item-left">
          <wd-icon name="user" size="32rpx" color="#667eea" />
          <text class="item-label">昵称</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.nickName || '未设置' }}</text>
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 企业名称 -->
      <view class="list-item" @click="editField<PERSON>and<PERSON>('companyName')">
        <view class="item-left">
          <wd-icon name="home" size="32rpx" color="#667eea" />
          <text class="item-label">企业名称</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.companyName || '未设置' }}</text>
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 手机号 -->
      <view class="list-item">
        <view class="item-left">
          <wd-icon name="phone" size="32rpx" color="#667eea" />
          <text class="item-label">手机号</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{ userStore.userInfo.phone || '未绑定' }}</text>
          <text class="item-tip">暂不支持修改</text>
        </view>
      </view>
    </view>

    <!-- 编辑弹窗 - 使用居中显示 -->
    <wd-popup 
      v-model="showEditPopup" 
      position="center"
      :close-on-click-modal="true"
    >
      <view class="edit-popup">
        <view class="popup-header">
          <text class="popup-title">编辑{{ editFieldLabel }}</text>
          <wd-icon name="close" size="32rpx" @click="closeEditPopup" />
        </view>
        <view class="popup-content">
          <wd-input
            v-model="editValue"
            :placeholder="`请输入${editFieldLabel}`"
            clearable
            :maxlength="50"
          />
        </view>
        <view class="popup-actions">
          <wd-button 
            type="default" 
            size="large" 
            custom-class="cancel-btn"
            @click="closeEditPopup"
          >
            取消
          </wd-button>
          <wd-button 
            type="primary" 
            size="large" 
            custom-class="save-btn"
            @click="saveEdit"
            :loading="saving"
          >
            保存
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

// 编辑相关状态
const showEditPopup = ref(false)
const editField = ref('')
const editFieldLabel = ref('')
const editValue = ref('')
const saving = ref(false)

// 页面加载时获取用户信息
onMounted(async () => {
  try {
    await userStore.getUserProfile()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})

// 编辑字段
const editFieldHandler = (field: string) => {
  const fieldMap: Record<string, string> = {
    nickName: '昵称',
    companyName: '企业名称'
  }

  editField.value = field
  editFieldLabel.value = fieldMap[field]
  editValue.value = userStore.userInfo[field as keyof typeof userStore.userInfo] as string || ''
  showEditPopup.value = true
}

// 关闭编辑弹窗
const closeEditPopup = () => {
  showEditPopup.value = false
  editField.value = ''
  editFieldLabel.value = ''
  editValue.value = ''
}

// 保存编辑
const saveEdit = async () => {
  if (!editValue.value.trim()) {
    toast.error(`${editFieldLabel.value}不能为空`)
    return
  }

  saving.value = true
  try {
    const updateData = {
      [editField.value]: editValue.value.trim()
    }
    
    await userStore.updateUserProfile(updateData)
    closeEditPopup()
    toast.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    toast.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}
</script>

<style lang="scss" scoped>
.profile-list {
  overflow: hidden;
}

.section-title {
  padding: 20rpx 24rpx 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fa;
  }
}

.item-left {
  display: flex;
  align-items: center;

  .item-label {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #333;
  }
}

.item-right {
  display: flex;
  align-items: center;

  .item-value {
    font-size: 26rpx;
    color: #606266;
    margin-right: 12rpx;
  }

  .item-tip {
    font-size: 22rpx;
    color: #909399;
    margin-right: 12rpx;
  }
}

.edit-popup {
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90vw;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;

  .popup-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }
}

.popup-content {
  margin-bottom: 48rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;

  .cancel-btn,
  .save-btn {
    flex: 1;
    height: 80rpx !important;
    border-radius: 40rpx !important;
    font-size: 28rpx !important;
  }
}

:deep(.save-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}
</style>