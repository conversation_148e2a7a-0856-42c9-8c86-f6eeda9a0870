<route lang="json">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "账户中心"
  }
}
</route>

<template>
  <view class="page-container gradient-bg-primary">
    <!-- 用户信息卡片 -->
    <view class="user-card common-card">
      <!-- 头像区域 -->
      <view class="avatar-section" @click="handleAvatarClick">
        <image
          :src="avatarUrl"
          class="avatar"
          mode="aspectFill"
        />
        <view class="avatar-edit-icon">
          <wd-icon name="camera" size="20rpx" color="#fff" />
        </view>
      </view>
      
      <!-- 用户基本信息 -->
      <view class="user-info">
        <text class="username">{{ userStore.userInfo.nickName || '未设置昵称' }}</text>
        <text class="user-phone">{{ userStore.userInfo.phone || '未绑定手机' }}</text>
        <text class="user-company">{{ userStore.userInfo.companyName || '未设置企业名称' }}</text>
      </view>
    </view>


    <!-- 账户设置列表 -->
    <view class="profile-list common-card">
      <view class="section-title">账户设置</view>
      
      <!-- 修改资料 -->
      <view class="list-item" @click="goToEditProfile">
        <view class="item-left">
          <wd-icon name="edit" size="32rpx" color="#667eea" />
          <text class="item-label">修改资料</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
      
      <!-- 修改密码 -->
      <view class="list-item" @click="goToChangePassword">
        <view class="item-left">
          <wd-icon name="lock-on" size="32rpx" color="#667eea" />
          <text class="item-label">修改密码</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>

    <!-- 其他操作列表 -->
    <view class="profile-list common-card">
      <view class="section-title">其他操作</view>

      <!-- 帮助中心 -->
      <view class="list-item" @click="goToHelpCenter">
        <view class="item-left">
          <wd-icon name="help" size="32rpx" color="#667eea" />
          <text class="item-label">帮助中心</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 意见反馈 -->
      <view class="list-item" @click="goToFeedback">
        <view class="item-left">
          <wd-icon name="edit" size="32rpx" color="#667eea" />
          <text class="item-label">意见反馈</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 关于我们 -->
      <view class="list-item" @click="goToAbout">
        <view class="item-left">
          <wd-icon name="user-talk" size="32rpx" color="#667eea" />
          <text class="item-label">关于我们</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 隐私政策 -->
      <view class="list-item" @click="goToPrivacyPolicy">
        <view class="item-left">
          <wd-icon name="error-circle" size="32rpx" color="#667eea" />
          <text class="item-label">隐私政策</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 服务条款 -->
      <view class="list-item" @click="goToTermsOfService">
        <view class="item-left">
          <wd-icon name="gift" size="32rpx" color="#667eea" />
          <text class="item-label">服务条款</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="list-item" @click="handleLogout">
        <view class="item-left">
          <wd-icon name="logout" size="32rpx" color="#f56c6c" />
          <text class="item-label logout-text">退出登录</text>
        </view>
        <view class="item-right">
          <wd-icon name="arrow-right" size="24rpx" color="#c0c4cc" />
        </view>
      </view>
    </view>


    <!-- 头像裁剪弹窗 -->
    <wd-popup
      v-model="showAvatarCropper"
      position="bottom"
      :safe-area-inset-bottom="true"
    >
      <view class="avatar-cropper-popup">
        <view class="popup-header">
          <text class="popup-title">裁剪头像</text>
          <wd-icon name="close" size="32rpx" @click="closeAvatarCropper" />
        </view>

        <!-- 图片预览区域 -->
        <view class="image-preview">
          <view v-if="!selectedImagePath" class="no-image-placeholder">
            <wd-icon name="image" size="48rpx" color="#c0c4cc" />
            <text class="placeholder-text">请选择图片</text>
          </view>
          <image
            v-else
            :src="selectedImagePath"
            class="preview-image"
            mode="aspectFill"
            @load="onImageLoad"
            @error="onImageError"
          />
          <view v-if="imageLoadError" class="image-error">
            <wd-icon name="error-circle" size="32rpx" color="#f56c6c" />
            <text class="error-text">图片加载失败</text>
          </view>
        </view>

        <!-- 裁剪提示 -->
        <view class="crop-tips">
          <text>建议使用正方形头像，系统会自动裁剪为圆形显示</text>
        </view>

        <view class="popup-actions">
          <wd-button
            type="default"
            size="large"
            custom-class="cancel-btn"
            @click="closeAvatarCropper"
          >
            取消
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            custom-class="upload-btn"
            @click="uploadAvatar"
            :loading="uploading"
          >
            {{ uploading ? '上传中...' : '确认上传' }}
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'
import { FileUploadUtil } from '@/utils/fileUpload'
import { useUserStoreAvatar } from '@/utils/imageUrl'
import { navigateToPage } from '@/utils'

const userStore = useUserStore()

// 使用头像URL处理组合式函数
const { avatarUrl } = useUserStoreAvatar()


// 头像上传相关状态
const showAvatarCropper = ref(false)
const selectedImagePath = ref('')
const uploading = ref(false)
const imageLoadError = ref(false)

// 页面加载时获取用户信息
onMounted(async () => {
  try {
    await userStore.getUserProfile()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})

// 处理头像点击 - 使用新的工具函数
const handleAvatarClick = async () => {
  try {
    const filePath = await FileUploadUtil.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 优先使用压缩版本
      sourceType: ['album', 'camera']
    })

    console.log('选择的图片路径:', filePath)
    selectedImagePath.value = filePath
    imageLoadError.value = false
    showAvatarCropper.value = true
  } catch (error: any) {
    console.error('选择图片失败:', error)
    toast.error(error.message || '选择图片失败')
  }
}

// 关闭头像裁剪弹窗
const closeAvatarCropper = () => {
  showAvatarCropper.value = false
  selectedImagePath.value = ''
  imageLoadError.value = false
}

// 图片加载成功
const onImageLoad = () => {
  console.log('图片加载成功')
  imageLoadError.value = false
}

// 图片加载失败
const onImageError = () => {
  console.error('图片加载失败')
  imageLoadError.value = true
}

// 上传头像 - 使用新的工具函数
const uploadAvatar = async () => {
  if (!selectedImagePath.value) {
    toast.error('请先选择图片')
    return
  }

  uploading.value = true
  try {
    console.log('开始上传头像，文件路径:', selectedImagePath.value)

    // 第一步：验证图片文件
    const isValid = await FileUploadUtil.validateImage(selectedImagePath.value)
    if (!isValid) {
      return
    }

    // 第二步：上传文件到服务器获取OSS地址
    const fileInfo = await FileUploadUtil.uploadFile(
      selectedImagePath.value,
      userStore.token,
      {
        classId: '0',
        onProgress: (progress) => {
          console.log('上传进度:', progress + '%')
        }
      }
    )

    console.log('文件上传成功，获取到的URL:', fileInfo.url)

    // 第三步：使用获取到的OSS地址更新用户头像
    await userStore.updateUserProfile({
      headerImg: fileInfo.url
    })

    closeAvatarCropper()
    toast.success('头像更新成功')
    console.log('头像更新流程完成')
  } catch (error: any) {
    console.error('头像上传失败:', error)
    toast.error(error.message || '头像上传失败，请重试')
  } finally {
    uploading.value = false
  }
}

// 跳转到修改资料页面
const goToEditProfile = () => {
  navigateToPage({
    url: '/pages/profile/edit-profile'
  })
}

// 跳转到修改密码页面
const goToChangePassword = () => {
  navigateToPage({
    url: '/pages/profile/change-password'
  })
}

// 跳转到帮助中心
const goToHelpCenter = () => {
  navigateToPage({
    url: '/pages/support/content-viewer?key=help&title=帮助中心'
  })
}

// 跳转到意见反馈
const goToFeedback = () => {
  navigateToPage({
    url: '/pages/support/feedback'
  })
}

// 跳转到关于我们
const goToAbout = () => {
  navigateToPage({
    url: '/pages/support/about'
  })
}

// 跳转到隐私政策
const goToPrivacyPolicy = () => {
  navigateToPage({
    url: '/pages/support/content-viewer?url=https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/privacy_policy.md&title=隐私政策'
  })
}

// 跳转到服务条款
const goToTermsOfService = () => {
  navigateToPage({
    url: '/pages/support/content-viewer?url=https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/service_terms.md&title=服务条款'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
        navigateToPage({
          url: '/pages/login/index'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
// 页面容器样式已移至全局样式文件

.user-card {
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
}

.avatar-section {
  position: relative;
  margin-right: 24rpx;

  .avatar {
    width: 128rpx;
    height: 128rpx;
    border-radius: 64rpx;
    border: 3rpx solid #fff;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  }

  .avatar-edit-icon {
    position: absolute;
    bottom: -2rpx;
    right: -2rpx;
    width: 32rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid #fff;
  }
}

.user-info {
  flex: 1;

  .username {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }

  .user-phone {
    display: block;
    font-size: 24rpx;
    color: #606266;
    margin-bottom: 6rpx;
  }

  .user-company {
    display: block;
    font-size: 24rpx;
    color: #909399;
  }
}

.profile-list {
  overflow: hidden;
}

.section-title {
  padding: 20rpx 24rpx 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fa;
  }
}

.item-left {
  display: flex;
  align-items: center;

  .item-label {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #333;
  }
}

.item-right {
  display: flex;
  align-items: center;

  .item-value {
    font-size: 26rpx;
    color: #606266;
    margin-right: 12rpx;
  }
}

.logout-text {
  color: #f56c6c !important;
}


.avatar-cropper-popup {
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.image-preview {
  margin: 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 320rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  flex: 1;

  .preview-image {
    width: 100%;
    height: 320rpx;
    border-radius: 12rpx;
    object-fit: cover;
  }

  .no-image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .placeholder-text {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #c0c4cc;
    }
  }

  .image-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .error-text {
      margin-top: 8rpx;
      font-size: 22rpx;
      color: #f56c6c;
    }
  }
}

.crop-tips {
  text-align: center;
  margin-bottom: 32rpx;

  text {
    font-size: 22rpx;
    color: #909399;
    line-height: 1.4;
  }
}

.popup-actions {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  margin-top: auto;
  
  :deep(.wd-button) {
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.upload-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}
</style>
