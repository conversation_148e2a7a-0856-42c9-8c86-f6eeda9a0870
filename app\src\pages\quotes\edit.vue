<route lang="jsonc" type="page">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "编辑报价"
  }
}</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import {
  createQuotation,
  updateQuotation,
  getQuotationDetail,
  saveQuotationDraft,
  publishQuotation as publishQuotationAPI
} from '@/api/quotation'
import type {
  ICreateQuotationRequest,
  IUpdateQuotationRequest,
  IQuotationResponse,
  QuotationPriceType,
  QuotationStatus,
  IQuotationExpiryOption,
  IInstrumentSelectItem
} from '@/types'
import InstrumentSelector from '@/components/InstrumentSelector.vue'
import { navigateBackOrTo } from '@/utils'

defineOptions({
  name: 'QuotationEdit'
})

// Store
const userStore = useUserStore()

// 页面状态
const isEdit = ref(false) // 是否为编辑模式
const quotationId = ref<number>()
const isLoading = ref(false)
const isSubmitting = ref(false)

// 表单数据
const formData = ref<ICreateQuotationRequest>({
  title: '',
  commodityName: '',
  deliveryLocation: '',
  brand: '',
  specifications: '',
  description: '',
  isBuyRequest: false, // 默认为出售
  priceType: 'Fixed',
  price: 0,
  instrumentRefID: undefined,
  expiresAt: '',
  status: 'Draft'
})

// 选择器数据 - 现在由组件内部管理
const selectedInstrument = ref<IInstrumentSelectItem>()

// 选择器引用
const expiryPicker = ref()
const customDatePicker = ref()
const formRef = ref()

// 有效期选项
const expiryOptions = ref<IQuotationExpiryOption[]>([
  {
    label: '当日有效',
    value: 'today',
    hours: 24,
    description: '到今日23:59过期'
  },
  {
    label: '3天内有效',
    value: '3days',
    hours: 72,
    description: '72小时后过期'
  },
  {
    label: '1周内有效',
    value: '1week',
    hours: 168,
    description: '7天后过期'
  },
  {
    label: '2周内有效',
    value: '2weeks',
    hours: 336,
    description: '14天后过期'
  },
  {
    label: '1个月内有效',
    value: '1month',
    hours: 720,
    description: '30天后过期'
  },
  {
    label: '自定义',
    value: 'custom',
    hours: 0,
    description: '请选择具体的过期时间'
  }
])

const selectedExpiryOption = ref('3days')
const customExpiryDate = ref('')

// 交易意向选择
const tradeIntentionValue = ref<'sell' | 'buy'>('sell') // 默认为出售

// 表单验证现在由 wot-design-uni 的 wd-form 组件处理

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入报价标题' }
  ],
  commodityName: [
    { required: true, message: '请输入商品名称' }
  ],
  deliveryLocation: [
    { required: true, message: '请输入交货地点' }
  ],
  // 价格验证现在动态处理
  expiresAt: [
    { required: true, message: '请设置有效期' }
  ]
}

// 是否显示期货合约选择
const showInstrumentSelector = computed(() => {
  return formData.value.priceType === 'Basis'
})

// 是否显示价格输入
const showPriceInput = computed(() => {
  return formData.value.priceType !== 'Negotiable'
})

// 价格验证规则动态调整
const priceValidationRule = computed(() => {
  if (formData.value.priceType === 'Negotiable') {
    return [] // 商议类型不需要验证价格
  }
  return [{ required: true, message: '请输入价格' }]
})

// 价格类型现在直接使用 formData.priceType


// 生命周期
onLoad((options) => {
  if (options?.id) {
    quotationId.value = parseInt(options.id)
    isEdit.value = true
    loadQuotationDetail()
  }

  // 设置默认有效期
  updateExpiryTime()
})

onMounted(() => {
  // 基础数据现在由组件内部加载
})

// 加载报价详情（编辑模式）
async function loadQuotationDetail() {
  if (!quotationId.value) return

  try {
    isLoading.value = true
    const res = await getQuotationDetail(quotationId.value)
    const quotation = res.data

    // 填充表单数据，确保类型正确
    formData.value = {
      title: quotation.title,
      commodityName: quotation.commodityName,
      deliveryLocation: quotation.deliveryLocation,
      brand: quotation.brand || '',
      specifications: quotation.specifications || '',
      description: quotation.description || '',
      isBuyRequest: quotation.isBuyRequest || false,
      priceType: typeof quotation.priceType === 'string' ? quotation.priceType as QuotationPriceType : 'Fixed',
      price: quotation.price,
      instrumentRefID: quotation.instrumentRefID,
      expiresAt: quotation.expiresAt
    }

    // 同步交易意向选择器的值
    tradeIntentionValue.value = quotation.isBuyRequest ? 'buy' : 'sell'

    // 设置选中的期货合约
    selectedInstrument.value = quotation.instrumentRef

    // 设置自定义过期时间
    selectedExpiryOption.value = 'custom'
    customExpiryDate.value = quotation.expiresAt

    uni.setNavigationBarTitle({
      title: '编辑报价'
    })

  } catch (error) {
    console.error('加载报价详情失败:', error)
    uni.showToast({
      title: '加载报价失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// 更新过期时间
function updateExpiryTime() {
  if (selectedExpiryOption.value === 'custom') {
    return // 自定义时间由用户选择
  }

  const option = expiryOptions.value.find(opt => opt.value === selectedExpiryOption.value)
  if (option) {
    const now = new Date()
    const expiryTime = new Date(now.getTime() + option.hours * 60 * 60 * 1000)
    formData.value.expiresAt = expiryTime.toISOString()
  }
}


// 期货合约选择变化
function onInstrumentChange(instrumentId: number, instrument: IInstrumentSelectItem | null) {
  formData.value.instrumentRefID = instrumentId || undefined
  selectedInstrument.value = instrument || undefined
}

// 交易意向改变
function onTradeIntentionChange(value: any) {
  const selectedValue = typeof value === 'string' ? value : value?.value
  tradeIntentionValue.value = selectedValue
  // 更新表单数据中的 isBuyRequest
  formData.value.isBuyRequest = selectedValue === 'buy'
}

// 价格类型改变
function onPriceTypeChange(priceType: QuotationPriceType) {
  // 更新表单数据
  formData.value.priceType = priceType

  // 处理副作用
  if (priceType === 'Fixed') {
    formData.value.instrumentRefID = undefined
    selectedInstrument.value = undefined
  }
}

// 有效期选项改变
function onExpiryOptionChange(value: any) {
  // 处理 wd-picker 的返回值
  const selectedValue = typeof value === 'string' ? value : value?.value || value?.detail?.value
  selectedExpiryOption.value = selectedValue

  if (selectedExpiryOption.value !== 'custom') {
    updateExpiryTime()
  }
}

// 自定义过期时间改变
function onCustomExpiryChange(value: any) {
  // 处理日期时间选择器的返回值
  const dateValue = typeof value === 'string' ? value : value?.detail?.value || value?.value
  customExpiryDate.value = dateValue

  if (dateValue) {
    formData.value.expiresAt = new Date(dateValue).toISOString()
  }
}

// 显示有效期选择器
function showExpiryPicker() {
  if (expiryPicker.value) {
    expiryPicker.value.open()
  }
}

// 显示自定义日期选择器
function showCustomDatePicker() {
  if (customDatePicker.value) {
    customDatePicker.value.open()
  }
}

// 保存草稿
async function saveDraft() {
  // 使用 wot-design-uni 表单验证
  try {
    const { valid } = await formRef.value.validate()
    if (!valid) {
      return
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    return
  }

  try {
    isSubmitting.value = true

    // 准备请求数据，确保数据类型正确
    const requestData = {
      ...formData.value,
      price: typeof formData.value.price === 'string' ? parseFloat(formData.value.price) || 0 : formData.value.price
    }

    if (isEdit.value && quotationId.value) {
      // 更新报价
      const updateData: IUpdateQuotationRequest = {
        id: quotationId.value,
        ...requestData
      }
      await updateQuotation(updateData)
    } else {
      // 创建草稿
      await saveQuotationDraft(requestData)
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })

    // 延迟返回上一页，如果没有上一页则跳转到报价管理页面
    setTimeout(() => {
      navigateBackOrTo('/pages/quotes/my-list')
    }, 1500)

  } catch (error) {
    console.error('保存草稿失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}

// 直接发布
async function publishQuotation() {
  // 使用 wot-design-uni 表单验证
  try {
    const { valid } = await formRef.value.validate()
    if (!valid) {
      return
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    return
  }

  try {
    isSubmitting.value = true

    // 准备请求数据，确保数据类型正确
    const requestData = {
      ...formData.value,
      price: typeof formData.value.price === 'string' ? parseFloat(formData.value.price) || 0 : formData.value.price
    }

    if (isEdit.value && quotationId.value) {
      // 更新报价数据
      const updateData: IUpdateQuotationRequest = {
        id: quotationId.value,
        ...requestData
      }
      await updateQuotation(updateData)

      // 发布报价（设置为激活状态并设置过期时间）
      await publishQuotationAPI({
        id: quotationId.value,
        expiresAt: requestData.expiresAt
      })
    } else {
      // 创建并直接发布
      const createData = {
        ...requestData,
        status: 'Active' as QuotationStatus
      }
      await saveQuotationDraft(createData)
    }

    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })

    // 延迟返回上一页，如果没有上一页则跳转到报价管理页面
    setTimeout(() => {
      navigateBackOrTo('/pages/quotes/my-list')
    }, 1500)

  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({
      title: '发布失败',
      icon: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <view class="page-container gradient-bg-primary">

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <wd-loading type="ring" color="#667eea" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 表单内容 -->
    <view v-else class="form-container">
      <wd-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 基本信息 -->
        <wd-cell-group title="基本信息" border custom-class="form-section">
          <!-- 交易类型 -->
          <wd-cell title="交易意向" label="选择您的交易类型">
            <wd-radio-group
              v-model="tradeIntentionValue"
              @change="onTradeIntentionChange"
              inline
              size="large"
            >
              <wd-radio value="sell">出售</wd-radio>
              <wd-radio value="buy">求购</wd-radio>
            </wd-radio-group>
          </wd-cell>
          <!-- 报价标题 -->
          <wd-input
            v-model="formData.title"
            label="报价标题"
            label-width="160rpx"
            placeholder="请输入醒目的报价标题"
            required
            clearable
            :maxlength="100"
            show-word-limit
            prop="title"
          />

          <!-- 商品名称 -->
          <wd-input
            v-model="formData.commodityName"
            label="商品名称"
            label-width="160rpx"
            placeholder="请输入商品名称"
            required
            clearable
            :maxlength="100"
            prop="commodityName"
          />

          <!-- 交货地点 -->
          <wd-input
            v-model="formData.deliveryLocation"
            label="交货地点"
            label-width="160rpx"
            placeholder="请输入交货地点"
            required
            clearable
            :maxlength="100"
            prop="deliveryLocation"
          />

          <!-- 品牌 -->
          <wd-input
            v-model="formData.brand"
            label="品牌"
            label-width="160rpx"
            placeholder="请输入品牌（可选）"
            clearable
            :maxlength="50"
          />

          <!-- 规格说明 -->
          <wd-textarea
            v-model="formData.specifications"
            label="规格说明"
            label-width="160rpx"
            placeholder="请输入规格说明（可选）"
            :maxlength="500"
            show-word-limit
            :auto-height="true"
          />
        </wd-cell-group>

        <!-- 价格信息 -->
        <wd-cell-group title="价格信息" border custom-class="form-section">
          <!-- 价格类型 -->
          <wd-cell title="价格类型" required title-width="160rpx">
            <wd-radio-group
              shape="button"
              :model-value="formData.priceType"
              @update:model-value="onPriceTypeChange"
              inline
              size="large"
              custom-class="dj-price-type-radio-group"
            >
              <wd-radio value="Fixed" size="small">一口价</wd-radio>
              <wd-radio value="Basis" size="small">基差报价</wd-radio>
              <wd-radio value="Negotiable" size="small">商议</wd-radio>
            </wd-radio-group>
          </wd-cell>

          <!-- 期货合约选择（基差报价时显示） -->
          <InstrumentSelector
            v-if="showInstrumentSelector"
            v-model="formData.instrumentRefID"
            label="期货合约"
            label-width="160rpx"
            placeholder="请选择期货合约"
            required
            @change="onInstrumentChange"
          />

          <!-- 价格 -->
          <wd-input
            v-if="showPriceInput"
            v-model="formData.price"
            :label="formData.priceType === 'Fixed' ? '价格' : '基差值'"
            label-width="160rpx"
            :placeholder="formData.priceType === 'Fixed' ? '请输入价格' : '请输入基差值'"
            type="digit"
            required
            prop="price"
            :rules="priceValidationRule"
          >
            <template #suffix>
              <text class="price-unit">{{ formData.priceType === 'Fixed' ? '元' : '点' }}</text>
            </template>
          </wd-input>
          
          <!-- 商议类型提示 -->
          <wd-cell 
            v-else
            title="价格"
            value="价格面议，可通过沟通协商"
            label=""
          />
        </wd-cell-group>

        <!-- 有效期设置 -->
        <wd-cell-group title="有效期设置" border custom-class="form-section">
          <!-- 有效期选择器 -->
          <wd-picker
            label="有效期"
            label-width="160rpx"
            placeholder="请选择有效期"
            ref="expiryPicker"
            v-model="selectedExpiryOption"
            :columns="expiryOptions"
            label-key="label"
            value-key="value"
            @confirm="onExpiryOptionChange"
            required
            prop="expiresAt"
          />

          <!-- 自定义日期时间选择器 -->
          <wd-datetime-picker
            v-if="selectedExpiryOption === 'custom'"
            label="自定义过期时间"
            label-width="160rpx"
            ref="customDatePicker"
            v-model="customExpiryDate"
            type="date"
            @confirm="onCustomExpiryChange"
            required
          />

          <!-- 补充说明 -->
          <wd-textarea
            v-model="formData.description"
            label="补充说明"
            label-width="160rpx"
            placeholder="请输入补充说明（可选）"
            :maxlength="1000"
            show-word-limit
            :auto-height="true"
          />
        </wd-cell-group>
      </wd-form>
    </view>

    <!-- 底部操作按钮 -->
    <view class="form-actions">
      <wd-button
        type="info"
        custom-class="dj-btn-secondary"
        :loading="isSubmitting"
        @click="saveDraft"
      >
        {{ isEdit ? '保存修改' : '保存草稿' }}
      </wd-button>

      <wd-button
        type="primary"
        custom-class="dj-btn-primary"
        :loading="isSubmitting"
        @click="publishQuotation"
      >
        {{ isEdit ? '更新并发布' : '直接发布' }}
      </wd-button>
    </view>



  </view>
</template>

<style lang="scss" scoped>
// 基础变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$secondary-color: #764ba2;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$border-color: #e0e3ea;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$box-shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$transition-base: all 0.3s ease;
$font-size-large: 32rpx;
$font-size-medium: 28rpx;
$font-size-small: 24rpx;
$font-size-title: 36rpx;
$font-size-page-title: 40rpx;

// 页面容器样式已移至全局样式文件
// 页面基本布局调整
.page-container {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom, 0px)); // 增加底部边距，避免遮挡，并适配安全区域
  padding-top: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  .loading-text {
    margin-top: 20rpx;
    font-size: $font-size-medium;
    color: $text-secondary;
  }
}

.form-container {
  padding: 0 20rpx 60rpx; // 增加底部内边距，确保字数统计有足够空间
  overflow-y: auto; // 确保内容可以滚动
}

// 表单分区 - 使用 wd-cell-group 的自定义样式
:deep(.form-section) {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

// 价格类型选择器样式现在通过 customClass 处理

.cell-value {
  color: $text-primary;
  font-size: $font-size-medium;
}

.price-unit {
  color: $text-secondary;
  font-size: $font-size-small;
  margin-left: 10rpx;
}

// 底部操作按钮
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom, 0px)); // 适配安全区域
  background: white;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.15); // 增强阴影效果
  z-index: 1000; // 确保按钮在最上层
  backdrop-filter: blur(10rpx); // 添加背景模糊效果
  -webkit-backdrop-filter: blur(10rpx); // Safari兼容

  :deep(.wd-button) {
    flex: 1;
    height: 88rpx; // 明确设置按钮高度
    border-radius: 12rpx; // 圆角
  }
}

// 深度选择器样式
:deep() {
  .dj-btn-primary {
    background: $primary-gradient;
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-btn-secondary {
    background: #f8f9fa;
    border: 1rpx solid #e0e3ea;
    color: $text-primary;
    font-weight: 500;
    transition: $transition-base;

    &:hover {
      background: #e9ecef;
      border-color: #d0d3d9;
    }
  }



  // 价格类型选择器样式
  .dj-price-type-radio-group {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;

    .wd-radio {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    // 通过深度选择器设置按钮样式
    :deep(.wd-radio__button) {
      font-size: 24rpx !important;
      min-height: auto !important;
    }
  }

  // 降低字数统计的 z-index，避免遮挡按钮
  .wd-textarea__count {
    z-index: 1 !important;
  }
}
</style>