<route lang="json">
{
  "style": {
    "navigationBarTitleText": "关于我们"
  }
}
</route>

<template>
  <view class="about-container">
    <!-- 应用信息卡片 -->
    <view class="app-info-card common-card">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image 
          src="/static/logo.svg" 
          class="app-logo" 
          mode="aspectFit"
          @error="handleLogoError"
        />
        <text class="app-name">点价交易</text>
        <text class="app-slogan">专业的大宗商品交易平台</text>
      </view>
      
      <!-- 版本信息 -->
      <view class="version-info">
        <view class="version-item">
          <text class="version-label">当前版本</text>
          <text class="version-value">{{ appVersion }}</text>
        </view>
        <view class="version-item">
          <text class="version-label">构建时间</text>
          <text class="version-value">{{ buildTime }}</text>
        </view>
      </view>
    </view>

    <!-- 公司信息 -->
    <view class="company-info common-card">
      <view class="section-title">公司信息</view>
      
      <view class="info-item">
        <view class="item-left">
          <wd-icon name="home" size="32rpx" color="#667eea" />
          <text class="item-label">公司名称</text>
        </view>
        <text class="item-value">上海点价科技有限公司</text>
      </view>
      
      <view class="info-item">
        <view class="item-left">
          <wd-icon name="link" size="32rpx" color="#667eea" />
          <text class="item-label">官方网站</text>
        </view>
        <text class="item-value link-text" @click="openWebsite">www.yongpaikeji.com</text>
      </view>
      
      <!-- 客服二维码 -->
      <view class="qrcode-section">
        <view class="qrcode-title">客服二维码</view>
        <view class="qrcode-container">
          <image 
            src="https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/my_qrcode.png" 
            class="qrcode-image" 
            mode="aspectFit"
          />
          <text class="qrcode-hint">长按自动扫描</text>
        </view>
      </view>
      
    </view>


    <!-- 版权信息 -->
    <view class="copyright">
      <text class="copyright-text">© 2024 上海点价科技有限公司</text>
      <text class="copyright-text">版权所有 保留一切权利</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { toast } from '@/utils/toast'

// 应用信息
const appVersion = ref('V1.0')
const buildTime = ref('2025年08月05日')

// 处理Logo加载错误
const handleLogoError = () => {
  console.log('Logo加载失败，使用默认图标')
}

// 打开官网
const openWebsite = () => {
  uni.showModal({
    title: '打开网站',
    content: '是否要在浏览器中打开官方网站？',
    success: (res) => {
      if (res.confirm) {
        // #ifdef H5
        window.open('https://www.dianjia.com', '_blank')
        // #endif
        
        // #ifndef H5
        uni.showToast({
          title: '请在浏览器中访问 www.dianjia.com',
          icon: 'none',
          duration: 3000
        })
        // #endif
      }
    }
  })
}

</script>

<style lang="scss" scoped>
.about-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 32rpx;
}

.app-info-card {
  padding: 60rpx 40rpx;
  text-align: center;
}

.logo-section {
  margin-bottom: 40rpx;
  
  .app-logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }
  
  .app-name {
    display: block;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 12rpx;
  }
  
  .app-slogan {
    display: block;
    font-size: 26rpx;
    color: #909399;
  }
}

.version-info {
  display: flex;
  justify-content: space-around;
  padding-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.version-item {
  text-align: center;
  
  .version-label {
    display: block;
    font-size: 24rpx;
    color: #909399;
    margin-bottom: 8rpx;
  }
  
  .version-value {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }
}

.company-info {
  overflow: hidden;
}

.section-title {
  padding: 32rpx 40rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:last-child {
    border-bottom: none;
  }
}

.item-left {
  display: flex;
  align-items: center;
  
  .item-label {
    margin-left: 24rpx;
    font-size: 30rpx;
    color: #333;
  }
}

.item-value {
  font-size: 28rpx;
  color: #606266;
  
  &.link-text {
    color: #667eea;
    text-decoration: underline;
  }
}

.qrcode-section {
  padding: 40rpx;
  text-align: center;
  border-top: 1rpx solid #f8f8f8;
  
  .qrcode-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 32rpx;
  }
  
  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .qrcode-image {
      width: 200rpx;
      height: 200rpx;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    }
    
    .qrcode-hint {
      font-size: 24rpx;
      color: #909399;
      background: #f5f7fa;
      padding: 12rpx 24rpx;
      border-radius: 20rpx;
    }
  }
}

.copyright {
  text-align: center;
  padding: 40rpx 0;
  
  .copyright-text {
    display: block;
    font-size: 24rpx;
    color: #c0c4cc;
    line-height: 1.6;
  }
}
</style>
