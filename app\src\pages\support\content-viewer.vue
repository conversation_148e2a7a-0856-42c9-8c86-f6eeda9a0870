<route lang="json">
{
  "style": {
    "navigationBarTitleText": "内容详情"
  }
}
</route>

<template>
  <view class="content-viewer">
    <view v-if="loading" class="loading-container">
      <wd-loading />
      <text class="loading-text">加载中...</text>
    </view>

    <view v-else-if="error" class="error-container">
      <wd-icon name="warning" size="64rpx" color="#f56c6c" />
      <text class="error-text">{{ error }}</text>
      <wd-button type="primary" size="small" @click="loadContent">重试</wd-button>
    </view>

    <scroll-view v-else class="content-scroll" scroll-y>
      <view class="content-body common-card">
        <zero-markdown-view :markdown="markdownContent" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 页面参数
interface PageParams {
  url?: string
  title?: string
}

const loading = ref(true)
const error = ref('')
const markdownContent = ref('')
const contentUrl = ref('')
const pageTitle = ref('内容详情')


// 加载内容
const loadContent = async () => {
  loading.value = true
  error.value = ''

  try {
    if (!contentUrl.value) {
      throw new Error('未提供内容URL')
    }

    // 从网络下载markdown文件
    const response = await uni.request({
      url: contentUrl.value,
      method: 'GET',
      responseType: 'text'
    })

    if (response.statusCode !== 200) {
      throw new Error(`请求失败: ${response.statusCode}`)
    }

    // 设置Markdown内容
    markdownContent.value = response.data as string

  } catch (err: any) {
    console.error('加载内容失败:', err)
    error.value = err.message || '加载失败，请重试'
  } finally {
    loading.value = false
  }
}

// 页面加载
onLoad((options: PageParams) => {
  console.log('页面参数:', options)
  contentUrl.value = options.url || 'https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/privacy_policy.md'
  if (options.title) {
    pageTitle.value = options.title
    uni.setNavigationBarTitle({
      title: options.title
    })
  }
})

onMounted(() => {
  loadContent()
})
</script>

<style lang="scss" scoped>
.content-viewer {
  height: 100vh;
  background: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;

  .loading-text {
    margin-top: 16rpx;
    font-size: 26rpx;
    color: #909399;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 32rpx;

  .error-text {
    margin: 16rpx 0 24rpx 0;
    font-size: 26rpx;
    color: #f56c6c;
    text-align: center;
  }
}

.content-scroll {
  height: 100vh;
}

.content-body {
  min-height: 100%;
  padding: 16rpx 12rpx;
}
</style>
