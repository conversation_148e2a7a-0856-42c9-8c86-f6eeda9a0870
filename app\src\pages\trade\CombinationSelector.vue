<template>
  <wd-select-picker
    class="combination-selector"
    :columns="combinations"
    :model-value="modelValue"
    @confirm="handleConfirm"
    @change="handleChange"
    :title="title"
    :disabled="disabled"
    type="radio"
    :show-confirm="!autoComplete"
    filterable
    filter-placeholder="搜索交易组合"
    use-default-slot
    root-portal
    :z-index="9999"
    safe-area-inset-bottom
  >
    <!-- 
      解决方案：使用内联样式确保在微信小程序中正确渲染
      原因：微信小程序中第三方组件的样式隔离机制导致外部CSS无法穿透
      内联样式具有最高优先级且不依赖样式穿透机制
    -->
    <view class="combination-card common-card" 
          style="border: 1rpx solid #e5e7eb;">
      <view class="combination-content" 
            style="display: flex !important; align-items: center !important; padding: 24rpx 20rpx; width: 100%; box-sizing: border-box;">
        <view class="combination-left" 
              style="flex: 1 !important; display: flex !important; flex-direction: column !important; align-items: center !important;">
          <text class="combination-label" 
                style="font-size: 22rpx; color: #64748b; margin-bottom: 8rpx; font-weight: 400;">被点价方</text>
          <text class="combination-name"
                style="font-size: 28rpx; color: #1e293b; font-weight: 600; text-align: center; line-height: 1.3;">{{ currentDisplay.setterName || '请选择' }}</text>
        </view>
        <view class="combination-divider" 
              style="width: 2rpx !important; height: 60rpx !important; background: #cbd5e1 !important; margin: 0 20rpx !important; opacity: 0.6; flex-shrink: 0 !important;"></view>
        <view class="combination-right" 
              style="flex: 1 !important; display: flex !important; flex-direction: column !important; align-items: center !important;">
          <text class="combination-label"
                style="font-size: 22rpx; color: #64748b; margin-bottom: 8rpx; font-weight: 400;">期货合约</text>
          <text class="combination-name"
                style="font-size: 28rpx; color: #1e293b; font-weight: 600; text-align: center; line-height: 1.3;">{{ currentDisplay.instrumentName || '交易组合' }}</text>
        </view>
      </view>
    </view>
  </wd-select-picker>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue'

// 类型定义
interface CombinationOption {
  label: string
  value: string
  setterID: number
  instrumentRefID: number
  setterName: string
  instrumentName: string
}

interface CurrentDisplay {
  setterName: string
  instrumentName: string
}

interface ConfirmEvent {
  value: string
  selectedItems?: CombinationOption[]
}

interface ChangeEvent {
  value: string
}

// Props 定义
const props = withDefaults(defineProps<{
  combinations: CombinationOption[]
  modelValue: string
  currentDisplay: CurrentDisplay
  title?: string
  disabled?: boolean
  autoComplete?: boolean
}>(), {
  title: '选择交易组合',
  disabled: false,
  autoComplete: true
})

// Events 定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

// 事件处理
function handleConfirm({ value }: ConfirmEvent) {
  console.log('SelectPicker confirm:', value)
  // 确保 value 存在且有效
  if (value !== undefined && value !== null) {
    // 更新 v-model
    emit('update:modelValue', value)
    // 发射变更事件
    emit('change', value)
  }
}

// 处理选择变更事件（用于自动完成）
function handleChange({ value }: ChangeEvent) {
  console.log('SelectPicker change:', value)
  // 确保 value 存在且有效
  if (value !== undefined && value !== null && props.autoComplete) {
    // 自动完成模式下，选择即确认
    emit('update:modelValue', value)
    emit('change', value)
  }
}
</script>

<style lang="scss" scoped>
/* 
  注意：微信小程序中第三方组件内的样式需要使用内联样式
  原因：外部CSS（包括深度选择器）无法穿透第三方组件的样式隔离
  
  以下样式仅在H5环境中生效，微信小程序环境使用内联样式
*/
.combination-selector {
  .combination-card {
    border: 1rpx solid #e5e7eb;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
    }

    .combination-content {
      display: flex;
      align-items: center;
      padding: 24rpx 20rpx;
      position: relative;
      width: 100%;
      box-sizing: border-box;

      .combination-left,
      .combination-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 0;

        .combination-label {
          font-size: 22rpx;
          color: #64748b;
          margin-bottom: 8rpx;
          font-weight: 400;
        }

        .combination-name {
          font-size: 28rpx;
          color: #1e293b;
          font-weight: 600;
          text-align: center;
          line-height: 1.3;
          word-break: break-all;
          overflow-wrap: break-word;
        }
      }

      .combination-divider {
        width: 2rpx;
        height: 60rpx;
        background: #cbd5e1;
        margin: 0 20rpx;
        opacity: 0.6;
        flex-shrink: 0;
      }
    }
  }
}
</style>
