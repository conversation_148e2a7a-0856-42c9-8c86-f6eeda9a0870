/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/workspace/index" |
       "/pages/contract/cancel-records" |
       "/pages/contract/detail" |
       "/pages/contract/form" |
       "/pages/contract/pricer-list" |
       "/pages/contract/setter-list" |
       "/pages/login/index" |
       "/pages/profile/change-password" |
       "/pages/profile/edit-profile" |
       "/pages/profile/index" |
       "/pages/quotes/detail" |
       "/pages/quotes/edit" |
       "/pages/quotes/marketplace" |
       "/pages/quotes/my-list" |
       "/pages/quotes/public-list" |
       "/pages/support/about" |
       "/pages/support/content-viewer" |
       "/pages/support/feedback" |
       "/pages/test/debug-select-input" |
       "/pages/test/market-demo" |
       "/pages/test/test-instrument-store" |
       "/pages/test/test-user-selector" |
       "/pages/trade/CombinationSelector" |
       "/pages/trade/execute" |
       "/pages/trade/pricer-management" |
       "/pages/trade/setter-management" |
       "/pages/trade/TradeOperationForm" |
       "/pages-sub/demo/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/workspace/index" | "/pages/quotes/my-list" | "/pages/trade/execute" | "/pages/profile/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
