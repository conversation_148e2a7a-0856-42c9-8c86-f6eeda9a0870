# 核心功能模块

> **版本**: 7.0.0  
> **负责人**: 产品开发团队  
> **状态**: 设计完成  
> **最后更新**: 2025-08-06

---

## 1. 功能模块概述

核心功能模块包含报价系统的基础功能，是整个系统的核心组成部分。主要包括报价管理、公开市场、用户主页和社交推广四个子模块。

### 1.1 模块关系图

```mermaid
graph TB
    A[报价管理] --> B[公开市场]
    B --> C[用户主页]
    C --> D[社交推广]
    A --> E[数据统计]
    B --> F[搜索筛选]
    C --> G[关注系统]
    D --> H[分享功能]
```

---

## 2. 报价管理模块

### 2.1 功能概述
报价管理模块为报价发布者提供完整的报价生命周期管理功能，包括创建、编辑、发布、撤回和删除报价。

### 2.2 核心功能

#### 2.2.1 创建/编辑报价
**功能描述**: 提供统一的表单页面，支持创建新报价或编辑现有草稿。

**页面路径**: `pages/quotes/edit.vue`

**表单字段**:
- **报价标题**: 必填，最多255字符
- **商品名称**: 必填，支持自动补全，最多100字符
- **商品分类**: 可选，下拉选择
- **交货地点**: 必填，最多255字符
- **品牌**: 可选，最多100字符
- **规格说明**: 可选，富文本编辑
- **补充说明**: 可选，富文本编辑
- **报价方式**: 必选，一口价/基差
- **价格**: 必填，数值输入
- **期货合约**: 基差报价时必填
- **有效期**: 必选，提供快捷选项

**交互设计**:
```mermaid
flowchart TD
    A[进入编辑页] --> B{是否为编辑模式}
    B -->|是| C[加载现有数据]
    B -->|否| D[初始化空表单]
    C --> E[填写表单]
    D --> E
    E --> F{选择报价方式}
    F -->|一口价| G[输入固定价格]
    F -->|基差| H[选择期货合约]
    G --> I[设置有效期]
    H --> I
    I --> J{选择操作}
    J -->|保存草稿| K[保存为Draft状态]
    J -->|直接发布| L[保存为Active状态]
    K --> M[返回列表页]
    L --> M
```

**验证规则**:
- 标题不能为空且不超过255字符
- 商品名称不能为空且不超过100字符
- 价格必须为正数，最多2位小数
- 有效期必须大于当前时间
- 基差报价必须选择期货合约

#### 2.2.2 我的报价列表
**功能描述**: 集中展示和管理当前用户创建的所有报价。

**页面路径**: `pages/quotes/my-list.vue`

**列表功能**:
- **状态筛选**: 有效报价(Active) / 无效报价(Draft, Expired, Withdrawn)
- **排序方式**: 创建时间、更新时间、过期时间
- **搜索功能**: 按标题、商品名称搜索
- **批量操作**: 批量删除草稿、批量发布

**列表项信息**:
- 报价标题和商品名称
- 价格和报价方式
- 状态标签和有效期
- 浏览量和关注数
- 操作按钮(编辑/发布/撤回/删除)

**状态操作**:
```mermaid
stateDiagram-v2
    [*] --> Draft : 创建草稿
    Draft --> Active : 发布
    Draft --> [*] : 删除
    Active --> Withdrawn : 撤回
    Active --> Expired : 过期
    Withdrawn --> Active : 重新发布
    Expired --> Active : 重新发布
    Withdrawn --> [*] : 删除
    Expired --> [*] : 删除
```

### 2.3 页面组件

#### 2.3.1 报价表单组件 (QuotationForm.vue)
```vue
<template>
  <form @submit.prevent="handleSubmit">
    <!-- 基础信息 -->
    <uni-section title="基础信息">
      <uni-forms-item label="报价标题" required>
        <uni-easyinput v-model="form.title" placeholder="请输入报价标题" />
      </uni-forms-item>
      <uni-forms-item label="商品名称" required>
        <uni-easyinput v-model="form.commodityName" placeholder="请输入商品名称" />
      </uni-forms-item>
      <!-- 其他字段... -->
    </uni-section>
    
    <!-- 价格信息 -->
    <uni-section title="价格信息">
      <uni-forms-item label="报价方式" required>
        <uni-data-select v-model="form.priceType" :localdata="priceTypes" />
      </uni-forms-item>
      <!-- 价格输入... -->
    </uni-section>
    
    <!-- 操作按钮 -->
    <view class="form-actions">
      <button type="button" @click="saveDraft">保存草稿</button>
      <button type="submit">直接发布</button>
    </view>
  </form>
</template>
```

#### 2.3.2 报价列表项组件 (QuotationListItem.vue)
```vue
<template>
  <view class="quotation-item">
    <view class="item-header">
      <text class="title">{{ quotation.title }}</text>
      <uni-tag :text="statusText" :type="statusType" />
    </view>
    <view class="item-content">
      <text class="commodity">{{ quotation.commodityName }}</text>
      <text class="price">{{ formatPrice(quotation.price, quotation.priceType) }}</text>
    </view>
    <view class="item-footer">
      <view class="stats">
        <text>浏览 {{ quotation.viewCount }}</text>
        <text>关注 {{ quotation.followCount }}</text>
      </view>
      <view class="actions">
        <button v-if="canEdit" @click="editQuotation">编辑</button>
        <button v-if="canPublish" @click="publishQuotation">发布</button>
        <button v-if="canWithdraw" @click="withdrawQuotation">撤回</button>
      </view>
    </view>
  </view>
</template>
```

---

## 3. 公开市场模块

### 3.1 功能概述
公开市场模块为所有用户提供浏览、搜索和发现报价的平台，支持多维度筛选和个性化推荐。

### 3.2 核心功能

#### 3.2.1 市场主页
**功能描述**: 展示所有公开的有效报价，支持搜索和筛选。

**页面路径**: `pages/quotes/marketplace.vue`

**页面布局**:
- **顶部搜索栏**: 支持关键词搜索
- **分类标签栏**: 动态显示用户关注的分类
- **筛选面板**: 价格类型、地区、时间等筛选条件
- **报价列表**: 瀑布流或列表形式展示报价
- **底部加载**: 支持下拉刷新和上拉加载

**个性化设计**:
```mermaid
flowchart TD
    A[用户进入市场] --> B{是否有关注分类}
    B -->|有| C[显示关注分类标签]
    B -->|无| D[显示引导页面]
    C --> E[默认显示关注内容]
    D --> F[引导选择分类]
    F --> G[跳转分类管理]
    G --> H[返回市场页面]
    H --> C
    E --> I[用户浏览报价]
    I --> J[点击报价详情]
```

#### 3.2.2 搜索和筛选
**搜索功能**:
- **关键词搜索**: 支持标题、商品名称、品牌搜索
- **智能提示**: 提供搜索建议和历史记录
- **搜索结果**: 高亮显示匹配关键词

**筛选功能**:
- **商品分类**: 多选分类筛选
- **报价方式**: 一口价/基差筛选
- **价格区间**: 自定义价格范围
- **交货地点**: 按地区筛选
- **发布时间**: 按时间范围筛选
- **排序方式**: 时间、价格、热度排序

#### 3.2.3 报价详情页
**功能描述**: 展示单个报价的详细信息，提供互动功能。

**页面路径**: `pages/quotes/detail.vue`

**页面内容**:
- **报价基础信息**: 标题、商品、价格、地点等
- **发布者信息**: 头像、昵称、公司信息
- **详细描述**: 规格说明、补充说明
- **统计信息**: 浏览量、关注数、发布时间
- **操作按钮**: 关注、分享、联系发布者

**权限控制**:
- **发布者视角**: 显示编辑按钮，不显示关注按钮
- **其他用户**: 显示关注按钮，显示发布者信息
- **未登录用户**: 只能浏览，不能操作

### 3.3 页面组件

#### 3.3.1 市场搜索组件 (MarketplaceSearch.vue)
```vue
<template>
  <view class="search-container">
    <uni-search-bar 
      v-model="searchKeyword"
      placeholder="搜索商品、品牌或公司"
      @search="handleSearch"
      @input="handleInput"
    />
    <view v-if="showSuggestions" class="suggestions">
      <view 
        v-for="item in suggestions" 
        :key="item.id"
        class="suggestion-item"
        @click="selectSuggestion(item)"
      >
        {{ item.text }}
      </view>
    </view>
  </view>
</template>
```

#### 3.3.2 筛选面板组件 (FilterPanel.vue)
```vue
<template>
  <uni-popup ref="filterPopup" type="bottom">
    <view class="filter-panel">
      <view class="filter-header">
        <text>筛选条件</text>
        <button @click="resetFilters">重置</button>
      </view>
      
      <view class="filter-section">
        <text class="section-title">商品分类</text>
        <uni-data-checkbox 
          v-model="filters.categories"
          :localdata="categoryOptions"
          multiple
        />
      </view>
      
      <view class="filter-section">
        <text class="section-title">报价方式</text>
        <uni-data-checkbox 
          v-model="filters.priceType"
          :localdata="priceTypeOptions"
        />
      </view>
      
      <view class="filter-actions">
        <button @click="applyFilters">确定</button>
        <button @click="closeFilter">取消</button>
      </view>
    </view>
  </uni-popup>
</template>
```

---

## 4. 用户主页模块

### 4.1 功能概述
用户主页模块展示特定用户的公开档案和所有有效报价，支持关注和社交互动。

### 4.2 核心功能

#### 4.2.1 公开用户主页
**功能描述**: 聚合展示特定用户的公开信息和报价列表。

**页面路径**: `pages/quotes/public-list.vue`

**页面结构**:
- **用户信息卡片**: 头像、昵称、公司、认证状态
- **统计数据**: 报价总数、关注者数量、加入时间
- **操作区域**: 关注按钮、分享按钮、联系方式
- **报价列表**: 该用户的所有有效报价
- **底部导航**: 返回市场、生成海报等

**权限差异**:
- **主页所有者**: 显示"管理我的报价"按钮
- **其他用户**: 显示关注按钮和联系方式
- **未登录用户**: 只能浏览，不能操作

#### 4.2.2 用户信息展示
**信息内容**:
- **基础信息**: 用户名、头像、认证标识
- **公司信息**: 公司名称、所在地区、主营业务
- **联系方式**: 电话、邮箱(脱敏显示)
- **统计数据**: 发布报价数、获得关注数、注册时间

**数据获取**:
```javascript
// 获取用户公开档案
async getUserProfile(userId) {
  const response = await api.get(`/users/${userId}/profile`);
  return {
    id: response.id,
    username: response.username,
    avatar: response.avatar,
    companyName: response.companyName,
    location: response.location,
    phone: response.phone ? maskPhone(response.phone) : null,
    email: response.email ? maskEmail(response.email) : null,
    quotationCount: response.quotationCount,
    followerCount: response.followerCount,
    joinedAt: response.joinedAt
  };
}
```

### 4.3 页面组件

#### 4.3.1 用户信息卡片组件 (UserProfileCard.vue)
```vue
<template>
  <view class="profile-card">
    <view class="user-info">
      <image :src="user.avatar" class="avatar" />
      <view class="info-text">
        <text class="username">{{ user.username }}</text>
        <text class="company">{{ user.companyName }}</text>
        <text class="location">{{ user.location }}</text>
      </view>
    </view>
    
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-value">{{ user.quotationCount }}</text>
        <text class="stat-label">报价</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ user.followerCount }}</text>
        <text class="stat-label">关注者</text>
      </view>
    </view>
    
    <view class="user-actions">
      <button v-if="!isOwner" @click="toggleFollow">
        {{ isFollowing ? '已关注' : '关注' }}
      </button>
      <button v-if="isOwner" @click="goToMyList">
        管理我的报价
      </button>
      <button @click="generatePoster">生成海报</button>
    </view>
  </view>
</template>
```

---

## 5. 社交推广模块

### 5.1 功能概述
社交推广模块提供分享和海报生成功能，帮助用户扩大报价的传播范围。

### 5.2 核心功能

#### 5.2.1 微信小程序分享
**分享入口**:
- 我的报价列表页面
- 用户主页页面
- 报价详情页面

**分享内容**:
- **标题**: 动态生成，如"快来看看XXX的最新报价"
- **描述**: 包含主要商品和价格信息
- **图片**: 用户头像或报价商品图片
- **路径**: 指向具体的页面路径

#### 5.2.2 海报生成功能
**功能描述**: 生成包含报价信息和小程序码的分享海报。

**海报模板**:
- **顶部**: 用户头像、企业名称、联系方式
- **中部**: 精选的2-3条最新报价信息
- **底部**: 小程序码和引导文案

**生成流程**:
```mermaid
flowchart TD
    A[用户点击生成海报] --> B[获取用户信息]
    B --> C[获取报价数据]
    C --> D[生成小程序码]
    D --> E[渲染海报模板]
    E --> F[生成图片]
    F --> G[保存到相册]
    G --> H[显示分享选项]
```

### 5.3 页面组件

#### 5.3.1 海报生成组件 (QuotationPoster.vue)
```vue
<template>
  <view class="poster-container">
    <canvas 
      canvas-id="posterCanvas"
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
    />
    <view class="poster-actions">
      <button @click="generatePoster">生成海报</button>
      <button @click="savePoster">保存到相册</button>
      <button @click="sharePoster">分享海报</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      canvasWidth: 750,
      canvasHeight: 1334,
      posterData: null
    };
  },
  methods: {
    async generatePoster() {
      const ctx = uni.createCanvasContext('posterCanvas', this);
      
      // 绘制背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      
      // 绘制用户信息
      await this.drawUserInfo(ctx);
      
      // 绘制报价信息
      await this.drawQuotations(ctx);
      
      // 绘制小程序码
      await this.drawQRCode(ctx);
      
      ctx.draw();
    }
  }
};
</script>
```

---

## 6. 模块集成

### 6.1 模块间通信
各模块通过事件总线和状态管理进行通信：

```javascript
// 事件定义
const EVENTS = {
  QUOTATION_CREATED: 'quotation:created',
  QUOTATION_UPDATED: 'quotation:updated',
  QUOTATION_DELETED: 'quotation:deleted',
  USER_FOLLOWED: 'user:followed',
  USER_UNFOLLOWED: 'user:unfollowed'
};

// 事件监听
uni.$on(EVENTS.QUOTATION_CREATED, (quotation) => {
  // 更新相关页面数据
});
```

### 6.2 数据流管理
使用 Vuex 管理全局状态：

```javascript
// store/modules/quotation.js
export default {
  namespaced: true,
  state: {
    myQuotations: [],
    marketQuotations: [],
    currentQuotation: null,
    filters: {}
  },
  mutations: {
    SET_MY_QUOTATIONS(state, quotations) {
      state.myQuotations = quotations;
    },
    SET_MARKET_QUOTATIONS(state, quotations) {
      state.marketQuotations = quotations;
    }
  },
  actions: {
    async fetchMyQuotations({ commit }) {
      const quotations = await api.getMyQuotations();
      commit('SET_MY_QUOTATIONS', quotations);
    }
  }
};
```
