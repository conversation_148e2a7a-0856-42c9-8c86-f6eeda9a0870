# 高级功能模块

> **版本**: 7.0.0  
> **负责人**: 高级功能团队  
> **状态**: 设计完成  
> **最后更新**: 2025-08-06

---

## 1. 功能模块概述

高级功能模块包含报价系统的增强功能，提升用户体验和平台价值。主要包括个性化分类筛选、关注通知系统、浏览统计分析三个子模块。

### 1.1 模块架构图

```mermaid
graph TB
    A[个性化分类筛选] --> D[用户体验优化]
    B[关注通知系统] --> E[用户粘性提升]
    C[浏览统计分析] --> F[数据驱动决策]
    
    A --> G[智能推荐]
    B --> H[实时通知]
    C --> I[趋势分析]
    
    D --> J[平台价值]
    E --> J
    F --> J
```

---

## 2. 个性化分类筛选模块

### 2.1 功能概述
解决在大量交易品种下传统筛选模式效率低下的问题，采用"双层动态个性化"模型，为用户提供个性化的浏览体验。

### 2.2 核心设计理念

#### 2.2.1 双层架构设计
**第一层：个性化市场主页 (高频使用)**
- 用户进入报价市场后的主界面
- 将通用"筛选"功能升级为"品类切换"功能
- 基于用户关注列表动态生成内容

**第二层：全品类选择与管理 (低频使用)**
- 独立的品种管理页面
- 仅在用户需要调整关注范围时进入
- 提供高效的品种发现与管理功能

### 2.3 详细功能设计

#### 2.3.1 动态品类标签栏
**页面路径**: `pages/quotes/marketplace.vue`

**功能特性**:
- **横向可滚动**: 支持左右滑动浏览标签
- **动态生成**: 根据用户关注列表实时更新
- **固定标签**: "关注"标签始终显示在首位
- **管理入口**: "+ 管理"按钮位于标签栏末尾

**标签内容构成**:
1. **"关注"标签 (固定)**: 聚合展示所有已关注品种的报价
2. **已关注品类标签 (动态)**: 每个关注品种生成独立标签
3. **"+ 管理"按钮 (固定)**: 进入品种管理页面的唯一入口

**交互流程**:
```mermaid
flowchart TD
    A[用户进入市场页] --> B{是否有关注分类}
    B -->|有| C[显示动态标签栏]
    B -->|无| D[显示引导界面]
    C --> E[默认选中关注标签]
    D --> F[引导选择分类]
    F --> G[跳转管理页面]
    G --> H[选择关注分类]
    H --> I[返回市场页]
    I --> C
    E --> J[用户切换标签]
    J --> K[更新报价列表]
```

#### 2.3.2 全品类管理页面
**页面路径**: `pages/quotes/category-management.vue`

**页面定位**: 全屏或半屏面板，专注于品种发现与管理

**核心布局**:
- **顶部搜索框**: 支持品种名称或拼音首字母搜索
- **索引列表**: A-Z分组显示所有可用交易品种
- **快速索引**: 右侧索引条，快速定位到指定字母
- **关注按钮**: 每个品种项配有星形关注按钮

**交互设计**:
```mermaid
flowchart TD
    A[进入管理页面] --> B[加载所有分类]
    B --> C[显示索引列表]
    C --> D{用户操作}
    D -->|搜索| E[实时过滤结果]
    D -->|点击索引| F[跳转到对应字母]
    D -->|点击关注| G[切换关注状态]
    E --> H[更新显示列表]
    F --> H
    G --> I[更新本地存储]
    I --> J[更新按钮状态]
    D -->|返回| K[刷新市场页标签栏]
```

### 2.4 数据存储策略

#### 2.4.1 本地存储
```javascript
// 用户关注的分类ID列表
const USER_SELECTED_CATEGORIES = 'user_selected_categories';

// 存储结构
{
  categoryIds: [101, 102, 205], // 分类ID数组
  lastUpdated: '2025-08-06T10:30:00Z', // 最后更新时间
  version: '1.0' // 数据版本
}
```

#### 2.4.2 服务端同步
```javascript
// 同步用户关注分类到服务端
async syncUserCategories(categoryIds) {
  try {
    await api.post('/user/categories/sync', {
      categoryIds: categoryIds,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.warn('分类同步失败，使用本地数据');
  }
}
```

### 2.5 页面组件

#### 2.5.1 动态标签栏组件 (DynamicCategoryTabs.vue)
```vue
<template>
  <scroll-view scroll-x class="category-tabs">
    <view class="tab-container">
      <view 
        v-for="tab in tabs" 
        :key="tab.id"
        :class="['tab-item', { active: activeTab === tab.id }]"
        @click="selectTab(tab)"
      >
        <text>{{ tab.name }}</text>
        <text v-if="tab.count" class="count">({{ tab.count }})</text>
      </view>
      <view class="tab-item manage-btn" @click="openCategoryManagement">
        <text>+ 管理</text>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'followed',
      tabs: []
    };
  },
  async mounted() {
    await this.loadUserCategories();
    this.generateTabs();
  },
  methods: {
    async loadUserCategories() {
      const categoryIds = uni.getStorageSync(USER_SELECTED_CATEGORIES) || [];
      this.userCategories = await this.getCategoriesByIds(categoryIds);
    },
    generateTabs() {
      this.tabs = [
        { id: 'followed', name: '关注', count: this.getTotalCount() },
        ...this.userCategories.map(cat => ({
          id: cat.id,
          name: cat.name,
          count: cat.quotationCount
        }))
      ];
    }
  }
};
</script>
```

#### 2.5.2 分类管理组件 (CategoryManagement.vue)
```vue
<template>
  <view class="category-management">
    <view class="search-section">
      <uni-search-bar 
        v-model="searchKeyword"
        placeholder="搜索分类名称"
        @input="handleSearch"
      />
    </view>
    
    <view class="category-list">
      <uni-indexed-list 
        :options="indexedCategories"
        :show-select="false"
      >
        <template v-slot:default="{ item }">
          <view class="category-item">
            <text class="category-name">{{ item.name }}</text>
            <uni-icons 
              :type="item.isFollowed ? 'star-filled' : 'star'"
              :color="item.isFollowed ? '#ffca3e' : '#c0c4cc'"
              @click="toggleFollow(item)"
            />
          </view>
        </template>
      </uni-indexed-list>
    </view>
    
    <view class="action-bar">
      <button @click="saveAndReturn" type="primary">
        保存并返回
      </button>
    </view>
  </view>
</template>
```

---

## 3. 关注通知系统模块

### 3.1 功能概述
提供双重关注机制和实时通知系统，增强用户互动和信息粘性。支持用户级关注和报价级关注，在内容更新时向关注者推送通知。

### 3.2 关注机制设计

#### 3.2.1 双重关注类型
**用户关注 (User Follow)**:
- 关注特定用户，接收其所有新报价通知
- 关注入口：用户主页的关注按钮
- 通知触发：被关注用户发布新报价时

**报价关注 (Quotation Follow)**:
- 关注特定报价，接收该报价的更新通知
- 关注入口：报价详情页的关注按钮
- 通知触发：被关注报价更新时

#### 3.2.2 关注规则
- 用户不能关注自己
- 用户不能关注自己发布的报价
- 同一用户对同一对象只能关注一次
- 支持取消关注操作

### 3.3 通知系统设计

#### 3.3.1 通知类型定义
```javascript
const NOTIFICATION_TYPES = {
  QUOTATION_UPDATE: 'QuotationUpdate',    // 报价更新通知
  NEW_QUOTATION: 'NewQuotation',          // 新报价通知
  USER_FOLLOW: 'UserFollow'               // 用户关注通知
};
```

#### 3.3.2 通知触发机制
```mermaid
flowchart TD
    A[用户操作] --> B{操作类型}
    B -->|发布新报价| C[查找关注该用户的人]
    B -->|更新报价| D[查找关注该报价的人]
    B -->|关注用户| E[通知被关注用户]
    
    C --> F[生成新报价通知]
    D --> G[生成更新通知]
    E --> H[生成关注通知]
    
    F --> I[推送通知]
    G --> I
    H --> I
    
    I --> J[更新通知列表]
    J --> K[发送推送消息]
```

### 3.4 核心功能实现

#### 3.4.1 关注功能
**页面集成**:
- `pages/quotes/public-list.vue`: 用户主页关注按钮
- `pages/quotes/detail.vue`: 报价详情页关注按钮
- `pages/my/follows.vue`: 我的关注列表页

**关注状态管理**:
```javascript
// 关注状态检查
async checkFollowStatus(type, targetId) {
  const response = await api.get('/follows/status', {
    type: type,
    targetId: targetId
  });
  return response.isFollowed;
}

// 切换关注状态
async toggleFollow(type, targetId) {
  const isFollowed = await this.checkFollowStatus(type, targetId);
  
  if (isFollowed) {
    await api.delete('/follows', { type, id: targetId });
  } else {
    await api.post('/follows', { type, id: targetId });
  }
  
  // 更新UI状态
  this.updateFollowUI(!isFollowed);
}
```

#### 3.4.2 我的关注页面
**页面路径**: `pages/my/follows.vue`

**页面结构**:
- **顶部Tabs**: "关注的报价" / "关注的用户"
- **关注的报价**: 显示所有已关注的有效报价
- **关注的用户**: 显示所有已关注的用户
- **快捷操作**: 取消关注、查看详情

**数据过滤规则**:
- 关注的报价：只显示状态为 `Active` 的报价
- 已下架或过期的报价自动从列表中移除
- 支持按关注时间排序

#### 3.4.3 通知系统
**通知列表页**: `pages/notification/list.vue`

**通知展示**:
- **通知标题**: 根据类型动态生成
- **通知内容**: 包含相关报价或用户信息
- **时间显示**: 相对时间显示（如"2小时前"）
- **已读状态**: 区分已读和未读通知
- **快捷操作**: 标记已读、删除通知

**通知模板**:
```javascript
const NOTIFICATION_TEMPLATES = {
  QuotationUpdate: {
    title: '报价更新提醒',
    content: '您关注的"{quotationTitle}"报价已更新'
  },
  NewQuotation: {
    title: '新报价提醒', 
    content: '您关注的{username}发布了新报价"{quotationTitle}"'
  },
  UserFollow: {
    title: '关注提醒',
    content: '{username}关注了您'
  }
};
```

### 3.5 页面组件

#### 3.5.1 关注按钮组件 (FollowButton.vue)
```vue
<template>
  <button 
    :class="['follow-btn', { followed: isFollowed, loading: isLoading }]"
    @click="handleToggleFollow"
    :disabled="isLoading || isOwner"
  >
    <uni-icons 
      v-if="!isLoading"
      :type="isFollowed ? 'heart-filled' : 'heart'"
      size="16"
    />
    <uni-load-more v-if="isLoading" status="loading" />
    <text>{{ followText }}</text>
  </button>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      required: true,
      validator: value => ['User', 'Quotation'].includes(value)
    },
    targetId: {
      type: [String, Number],
      required: true
    },
    isOwner: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFollowed: false,
      isLoading: false
    };
  },
  computed: {
    followText() {
      if (this.isLoading) return '处理中...';
      return this.isFollowed ? '已关注' : '关注';
    }
  },
  async mounted() {
    await this.checkFollowStatus();
  },
  methods: {
    async checkFollowStatus() {
      try {
        this.isFollowed = await this.checkFollowStatus(this.type, this.targetId);
      } catch (error) {
        console.error('检查关注状态失败:', error);
      }
    },
    async handleToggleFollow() {
      if (this.isOwner || this.isLoading) return;
      
      this.isLoading = true;
      try {
        await this.toggleFollow(this.type, this.targetId);
        this.isFollowed = !this.isFollowed;
        this.$emit('follow-changed', {
          type: this.type,
          targetId: this.targetId,
          isFollowed: this.isFollowed
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>
```

#### 3.5.2 通知列表组件 (NotificationList.vue)
```vue
<template>
  <view class="notification-list">
    <view 
      v-for="notification in notifications"
      :key="notification.id"
      :class="['notification-item', { unread: !notification.isRead }]"
      @click="handleNotificationClick(notification)"
    >
      <view class="notification-content">
        <text class="title">{{ notification.title }}</text>
        <text class="content">{{ notification.content }}</text>
        <text class="time">{{ formatTime(notification.createdAt) }}</text>
      </view>
      <view class="notification-actions">
        <uni-icons 
          v-if="!notification.isRead"
          type="circle-filled"
          color="#007aff"
          size="8"
        />
        <uni-icons 
          type="trash"
          color="#999"
          @click.stop="deleteNotification(notification.id)"
        />
      </view>
    </view>
    
    <uni-load-more 
      :status="loadStatus"
      @clickLoadMore="loadMore"
    />
  </view>
</template>
```

---

## 4. 浏览统计分析模块

### 4.1 功能概述
为报价发布者提供完整的浏览量统计系统，追踪报价被访问的详细情况，包括总浏览量和每日趋势分析。

### 4.2 统计机制设计

#### 4.2.1 数据收集策略
**自动记录**: 用户进入报价详情页时自动记录浏览行为
**去重机制**: 同一用户同一天内的重复访问进行去重处理
**数据聚合**: 定时任务聚合原始数据，生成统计报表

#### 4.2.2 统计指标定义
- **总浏览量 (Total Views)**: 报价累计被浏览的次数
- **今日浏览量 (Today Views)**: 当天的浏览次数
- **独立访客 (Unique Visitors)**: 去重后的访问用户数
- **浏览趋势**: 过去30天的每日浏览量变化

### 4.3 数据处理流程

#### 4.3.1 浏览记录流程
```mermaid
flowchart TD
    A[用户进入详情页] --> B[检查是否已记录]
    B -->|未记录| C[记录浏览日志]
    B -->|已记录| D[跳过记录]
    C --> E[异步处理]
    D --> F[更新页面显示]
    E --> G[写入日志表]
    G --> H[返回成功状态]
    H --> F
```

#### 4.3.2 数据聚合流程
```mermaid
flowchart TD
    A[定时任务启动] --> B[扫描前一天日志]
    B --> C[按报价ID分组]
    C --> D[按用户ID去重]
    D --> E[计算UV和PV]
    E --> F[写入统计表]
    F --> G[更新报价计数]
    G --> H[清理过期日志]
```

### 4.4 核心功能实现

#### 4.4.1 浏览记录
**触发位置**: `pages/quotes/detail.vue`

```javascript
// 页面加载时记录浏览
async onLoad(options) {
  const quotationId = options.id;
  
  // 记录浏览行为
  await this.recordView(quotationId);
  
  // 加载报价详情
  await this.loadQuotationDetail(quotationId);
}

// 记录浏览方法
async recordView(quotationId) {
  try {
    await api.post(`/quotations/${quotationId}/record-view`);
  } catch (error) {
    console.warn('浏览记录失败:', error);
  }
}
```

#### 4.4.2 统计数据展示
**展示位置**: `pages/quotes/my-list.vue`

**数据获取**:
```javascript
// 获取我的报价列表（包含统计数据）
async loadMyQuotations() {
  const response = await api.get('/quotations/my-list');
  this.quotations = response.data.map(item => ({
    ...item,
    totalViewCount: item.totalViewCount || 0,
    todayViewCount: item.todayViewCount || 0,
    followCount: item.followCount || 0
  }));
}
```

#### 4.4.3 趋势分析图表
**图表组件**: `components/QuotationStatsChart.vue`

```vue
<template>
  <view class="stats-chart">
    <view class="chart-header">
      <text class="title">浏览趋势 (最近30天)</text>
      <text class="total">总浏览量: {{ totalViews }}</text>
    </view>
    
    <canvas 
      canvas-id="statsChart"
      :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }"
    />
    
    <view class="chart-legend">
      <view class="legend-item">
        <view class="legend-color" style="background: #007aff;"></view>
        <text>每日浏览量</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    quotationId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      chartWidth: 350,
      chartHeight: 200,
      statsData: [],
      totalViews: 0
    };
  },
  async mounted() {
    await this.loadStatsData();
    this.drawChart();
  },
  methods: {
    async loadStatsData() {
      try {
        const response = await api.get(`/quotations/${this.quotationId}/stats?days=30`);
        this.statsData = response.data;
        this.totalViews = this.statsData.reduce((sum, item) => sum + item.viewCount, 0);
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },
    drawChart() {
      const ctx = uni.createCanvasContext('statsChart', this);
      
      // 绘制坐标轴
      this.drawAxes(ctx);
      
      // 绘制数据线
      this.drawDataLine(ctx);
      
      // 绘制数据点
      this.drawDataPoints(ctx);
      
      ctx.draw();
    }
  }
};
</script>
```

### 4.5 性能优化

#### 4.5.1 防刷机制
```javascript
// 后端防刷逻辑
const RATE_LIMIT = {
  maxViews: 10,        // 每小时最大记录次数
  timeWindow: 3600000  // 时间窗口（1小时）
};

async function recordView(quotationId, userId, ip) {
  const key = `view_limit:${userId || ip}:${quotationId}`;
  const count = await redis.get(key) || 0;
  
  if (count >= RATE_LIMIT.maxViews) {
    throw new Error('访问过于频繁');
  }
  
  await redis.setex(key, RATE_LIMIT.timeWindow / 1000, count + 1);
  
  // 记录浏览日志
  await QuotationViewLog.create({
    quotationId,
    viewerUserId: userId,
    viewerIp: ip,
    viewedAt: new Date()
  });
}
```

#### 4.5.2 数据归档策略
```javascript
// 定时清理过期日志
async function cleanupOldLogs() {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - 90); // 保留90天
  
  await QuotationViewLog.destroy({
    where: {
      viewedAt: {
        [Op.lt]: cutoffDate
      }
    }
  });
}
```

---

## 5. 模块集成与协调

### 5.1 模块间依赖关系
```mermaid
graph TB
    A[个性化分类筛选] --> D[市场页面优化]
    B[关注通知系统] --> E[用户粘性提升]
    C[浏览统计分析] --> F[数据驱动优化]
    
    A --> G[用户行为数据]
    B --> G
    C --> G
    
    G --> H[智能推荐算法]
    G --> I[个性化内容]
```

### 5.2 数据流协调
各模块通过统一的数据接口和事件系统进行协调：

```javascript
// 全局事件总线
const EventBus = {
  // 分类关注变更
  onCategoryFollowChanged(categoryIds) {
    // 更新市场页面筛选
    this.$emit('marketplace:updateFilters', { categoryIds });
    // 更新推荐算法
    this.$emit('recommendation:updatePreferences', { categoryIds });
  },
  
  // 用户关注变更
  onUserFollowChanged(userId, isFollowed) {
    // 更新通知订阅
    this.$emit('notification:updateSubscription', { userId, isFollowed });
    // 更新统计数据
    this.$emit('stats:updateFollowCount', { userId, isFollowed });
  },
  
  // 浏览行为记录
  onViewRecorded(quotationId, userId) {
    // 更新推荐权重
    this.$emit('recommendation:updateWeight', { quotationId, userId });
    // 触发实时统计更新
    this.$emit('stats:incrementView', { quotationId });
  }
};
```

### 5.3 性能监控
```javascript
// 模块性能监控
const PerformanceMonitor = {
  // 分类筛选性能
  trackCategoryFilter(startTime, endTime, resultCount) {
    const duration = endTime - startTime;
    console.log(`分类筛选耗时: ${duration}ms, 结果数量: ${resultCount}`);
  },
  
  // 通知推送性能
  trackNotificationPush(notificationCount, successCount) {
    const successRate = (successCount / notificationCount) * 100;
    console.log(`通知推送成功率: ${successRate}%`);
  },
  
  // 统计计算性能
  trackStatsCalculation(quotationCount, duration) {
    console.log(`统计计算: ${quotationCount}条报价, 耗时: ${duration}ms`);
  }
};
```
