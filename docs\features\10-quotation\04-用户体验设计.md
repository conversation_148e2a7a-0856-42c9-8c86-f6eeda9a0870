# 用户体验设计

> **版本**: 7.0.0  
> **负责人**: UX设计团队  
> **状态**: 设计完成  
> **最后更新**: 2025-08-06

---

## 1. 设计理念与原则

### 1.1 设计理念
- **用户中心**: 以用户需求为核心，优化每个交互环节
- **简洁高效**: 减少用户认知负担，提高操作效率
- **一致性**: 保持界面元素和交互模式的一致性
- **可访问性**: 确保不同能力用户都能正常使用
- **移动优先**: 优先考虑移动端用户体验

### 1.2 设计原则
- **清晰性**: 信息层次清晰，重点突出
- **反馈性**: 及时提供操作反馈和状态提示
- **容错性**: 预防错误发生，提供错误恢复机制
- **效率性**: 减少操作步骤，提高任务完成效率
- **愉悦性**: 创造积极的情感体验

---

## 2. 页面架构设计

### 2.1 信息架构图

```mermaid
graph TB
    A[首页] --> B[报价市场]
    A --> C[我的报价]
    A --> D[我的关注]
    A --> E[通知中心]
    
    B --> F[报价详情]
    B --> G[用户主页]
    B --> H[分类管理]
    
    C --> I[创建报价]
    C --> J[编辑报价]
    
    D --> K[关注的报价]
    D --> L[关注的用户]
    
    F --> M[关注操作]
    F --> N[分享功能]
    G --> O[生成海报]
```

### 2.2 导航结构
**主导航 (TabBar)**:
- 报价市场 (首页)
- 我的报价
- 我的关注  
- 个人中心

**次级导航**:
- 面包屑导航
- 返回按钮
- 页面内标签切换

### 2.3 页面层级
- **L1**: 主要功能页面 (TabBar页面)
- **L2**: 详情和管理页面
- **L3**: 编辑和设置页面
- **L4**: 弹窗和确认页面

---

## 3. 界面设计规范

### 3.1 视觉设计系统

#### 3.1.1 色彩规范
```css
/* 主色调 */
--primary-color: #007AFF;      /* 主品牌色 */
--primary-light: #5AC8FA;      /* 主色调亮色 */
--primary-dark: #0051D5;       /* 主色调暗色 */

/* 辅助色 */
--success-color: #34C759;      /* 成功状态 */
--warning-color: #FF9500;      /* 警告状态 */
--error-color: #FF3B30;        /* 错误状态 */
--info-color: #5856D6;         /* 信息提示 */

/* 中性色 */
--text-primary: #000000;       /* 主要文字 */
--text-secondary: #3C3C43;     /* 次要文字 */
--text-tertiary: #8E8E93;      /* 辅助文字 */
--text-disabled: #C7C7CC;      /* 禁用文字 */

/* 背景色 */
--bg-primary: #FFFFFF;         /* 主背景 */
--bg-secondary: #F2F2F7;       /* 次背景 */
--bg-tertiary: #FFFFFF;        /* 卡片背景 */
--bg-overlay: rgba(0,0,0,0.4); /* 遮罩背景 */
```

#### 3.1.2 字体规范
```css
/* 字体大小 */
--font-size-h1: 28px;          /* 页面标题 */
--font-size-h2: 24px;          /* 区块标题 */
--font-size-h3: 20px;          /* 卡片标题 */
--font-size-body: 16px;        /* 正文内容 */
--font-size-caption: 14px;     /* 说明文字 */
--font-size-small: 12px;       /* 辅助信息 */

/* 字体权重 */
--font-weight-light: 300;
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-bold: 600;
```

#### 3.1.3 间距规范
```css
/* 间距系统 (8px基准) */
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 16px;
--spacing-lg: 24px;
--spacing-xl: 32px;
--spacing-xxl: 48px;

/* 组件间距 */
--component-margin: 16px;
--section-margin: 24px;
--page-padding: 16px;
```

### 3.2 组件设计规范

#### 3.2.1 按钮设计
```vue
<!-- 主要按钮 -->
<button class="btn btn-primary">
  <text>确认发布</text>
</button>

<!-- 次要按钮 -->
<button class="btn btn-secondary">
  <text>保存草稿</text>
</button>

<!-- 危险按钮 -->
<button class="btn btn-danger">
  <text>删除报价</text>
</button>

<style>
.btn {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-danger {
  background: var(--error-color);
  color: white;
}
</style>
```

#### 3.2.2 卡片设计
```vue
<view class="card">
  <view class="card-header">
    <text class="card-title">报价标题</text>
    <uni-tag text="有效" type="success" />
  </view>
  <view class="card-content">
    <text class="commodity-name">螺纹钢</text>
    <text class="price">¥4,200/吨</text>
  </view>
  <view class="card-footer">
    <view class="stats">
      <text>浏览 128</text>
      <text>关注 12</text>
    </view>
    <view class="actions">
      <button class="btn-small">编辑</button>
    </view>
  </view>
</view>

<style>
.card {
  background: var(--bg-tertiary);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
}
</style>
```

---

## 4. 交互设计

### 4.1 手势交互

#### 4.1.1 滑动操作
- **左滑删除**: 在列表项上左滑显示删除按钮
- **下拉刷新**: 在列表顶部下拉刷新数据
- **上拉加载**: 在列表底部上拉加载更多
- **横向滑动**: 分类标签栏支持横向滑动

#### 4.1.2 点击交互
- **单击**: 选择、确认、跳转
- **长按**: 显示上下文菜单、批量选择
- **双击**: 快速操作（如快速关注）

### 4.2 状态反馈

#### 4.2.1 加载状态
```vue
<template>
  <view class="loading-container">
    <!-- 骨架屏加载 -->
    <view v-if="isLoading" class="skeleton-list">
      <view v-for="i in 3" :key="i" class="skeleton-item">
        <view class="skeleton-avatar"></view>
        <view class="skeleton-content">
          <view class="skeleton-title"></view>
          <view class="skeleton-text"></view>
        </view>
      </view>
    </view>
    
    <!-- 实际内容 -->
    <view v-else class="content-list">
      <!-- 内容项 -->
    </view>
  </view>
</template>

<style>
.skeleton-item {
  display: flex;
  padding: 16px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f0f0f0;
}

@keyframes skeleton-loading {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}
</style>
```

#### 4.2.2 操作反馈
```javascript
// 成功反馈
uni.showToast({
  title: '发布成功',
  icon: 'success',
  duration: 2000
});

// 错误反馈
uni.showToast({
  title: '网络错误，请重试',
  icon: 'none',
  duration: 3000
});

// 确认对话框
uni.showModal({
  title: '确认删除',
  content: '删除后无法恢复，确定要删除这条报价吗？',
  confirmText: '删除',
  confirmColor: '#FF3B30',
  success: (res) => {
    if (res.confirm) {
      this.deleteQuotation();
    }
  }
});
```

### 4.3 动画效果

#### 4.3.1 页面转场
```css
/* 页面进入动画 */
.page-enter {
  transform: translateX(100%);
  opacity: 0;
}

.page-enter-active {
  transition: all 0.3s ease-out;
}

.page-enter-to {
  transform: translateX(0);
  opacity: 1;
}

/* 页面退出动画 */
.page-leave {
  transform: translateX(0);
  opacity: 1;
}

.page-leave-active {
  transition: all 0.3s ease-in;
}

.page-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
```

#### 4.3.2 组件动画
```css
/* 按钮点击效果 */
.btn:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 卡片悬停效果 */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transition: all 0.2s ease;
}

/* 关注按钮动画 */
.follow-btn.followed {
  animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
```

---

## 5. 响应式设计

### 5.1 屏幕适配

#### 5.1.1 断点设计
```css
/* 移动端断点 */
@media (max-width: 375px) {
  /* iPhone SE */
  .container { padding: 12px; }
  .card { padding: 12px; }
}

@media (min-width: 376px) and (max-width: 414px) {
  /* iPhone 标准尺寸 */
  .container { padding: 16px; }
  .card { padding: 16px; }
}

@media (min-width: 415px) {
  /* iPhone Plus/Max */
  .container { padding: 20px; }
  .card { padding: 20px; }
}
```

#### 5.1.2 字体缩放
```css
/* 基于视口的字体缩放 */
.responsive-text {
  font-size: clamp(14px, 4vw, 18px);
}

/* 标题自适应 */
.title {
  font-size: clamp(20px, 5vw, 28px);
  line-height: 1.2;
}
```

### 5.2 横竖屏适配

#### 5.2.1 布局调整
```css
/* 竖屏布局 */
@media (orientation: portrait) {
  .quotation-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 横屏布局 */
@media (orientation: landscape) {
  .quotation-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}
```

---

## 6. 无障碍设计

### 6.1 可访问性标准

#### 6.1.1 语义化标签
```vue
<template>
  <view class="quotation-item" role="article">
    <view class="item-header" role="banner">
      <text class="title" role="heading" aria-level="2">
        {{ quotation.title }}
      </text>
    </view>
    
    <view class="item-content" role="main">
      <text class="price" aria-label="价格">
        {{ formatPrice(quotation.price) }}
      </text>
    </view>
    
    <view class="item-actions" role="toolbar">
      <button 
        aria-label="编辑报价"
        @click="editQuotation"
      >
        编辑
      </button>
    </view>
  </view>
</template>
```

#### 6.1.2 键盘导航
```css
/* 焦点样式 */
.focusable:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

### 6.2 多语言支持

#### 6.2.1 国际化配置
```javascript
// i18n/zh-CN.js
export default {
  quotation: {
    title: '报价标题',
    commodity: '商品名称',
    price: '价格',
    location: '交货地点',
    expires: '有效期至',
    status: {
      draft: '草稿',
      active: '有效',
      expired: '已过期',
      withdrawn: '已撤回'
    }
  },
  actions: {
    edit: '编辑',
    delete: '删除',
    publish: '发布',
    follow: '关注',
    share: '分享'
  }
};
```

---

## 7. 性能优化

### 7.1 渲染优化

#### 7.1.1 虚拟列表
```vue
<template>
  <scroll-view 
    class="virtual-list"
    scroll-y
    @scroll="handleScroll"
    :scroll-top="scrollTop"
  >
    <view 
      v-for="item in visibleItems" 
      :key="item.id"
      class="list-item"
      :style="{ height: itemHeight + 'px' }"
    >
      <quotation-item :data="item" />
    </view>
  </scroll-view>
</template>

<script>
export default {
  data() {
    return {
      itemHeight: 120,
      visibleCount: 10,
      scrollTop: 0,
      startIndex: 0
    };
  },
  computed: {
    visibleItems() {
      const start = this.startIndex;
      const end = start + this.visibleCount;
      return this.allItems.slice(start, end);
    }
  },
  methods: {
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop;
      this.startIndex = Math.floor(scrollTop / this.itemHeight);
    }
  }
};
</script>
```

#### 7.1.2 图片懒加载
```vue
<template>
  <image 
    :src="imageSrc"
    :lazy-load="true"
    @load="handleImageLoad"
    @error="handleImageError"
    class="lazy-image"
  />
</template>

<script>
export default {
  props: {
    src: String,
    placeholder: {
      type: String,
      default: '/static/images/placeholder.png'
    }
  },
  data() {
    return {
      imageSrc: this.placeholder,
      isLoaded: false
    };
  },
  mounted() {
    this.loadImage();
  },
  methods: {
    loadImage() {
      const img = new Image();
      img.onload = () => {
        this.imageSrc = this.src;
        this.isLoaded = true;
      };
      img.src = this.src;
    }
  }
};
</script>
```

### 7.2 缓存策略

#### 7.2.1 数据缓存
```javascript
// 缓存管理器
class CacheManager {
  constructor() {
    this.cache = new Map();
    this.maxSize = 100;
    this.ttl = 5 * 60 * 1000; // 5分钟
  }
  
  set(key, data) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
}
```

---

## 8. 用户测试与反馈

### 8.1 可用性测试

#### 8.1.1 测试场景
1. **新用户首次使用**: 注册登录 → 浏览市场 → 关注报价
2. **发布者创建报价**: 创建报价 → 填写信息 → 发布成功
3. **用户搜索报价**: 搜索关键词 → 筛选结果 → 查看详情
4. **社交功能使用**: 关注用户 → 接收通知 → 分享海报

#### 8.1.2 评估指标
- **任务完成率**: 用户能否成功完成预定任务
- **任务完成时间**: 完成任务所需的平均时间
- **错误率**: 用户操作过程中的错误次数
- **满意度**: 用户对界面和交互的满意程度

### 8.2 反馈收集

#### 8.2.1 反馈渠道
- 应用内反馈按钮
- 用户调研问卷
- 客服系统记录
- 应用商店评价

#### 8.2.2 持续改进
- 定期分析用户行为数据
- 收集用户反馈意见
- 进行A/B测试验证
- 迭代优化设计方案
