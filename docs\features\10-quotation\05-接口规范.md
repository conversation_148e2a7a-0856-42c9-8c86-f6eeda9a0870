# 接口规范

> **版本**: 7.0.0  
> **负责人**: 后端开发团队  
> **状态**: 设计完成  
> **最后更新**: 2025-08-06

---

## 1. 接口设计原则

### 1.1 设计原则
- **RESTful风格**: 遵循REST架构风格，使用标准HTTP方法
- **统一响应格式**: 所有接口使用统一的响应数据结构
- **版本控制**: 通过URL路径进行API版本管理
- **安全认证**: 使用JWT Token进行身份认证
- **错误处理**: 提供详细的错误码和错误信息

### 1.2 基础规范

#### 1.2.1 请求格式
```
Base URL: https://api.example.com/v1
Content-Type: application/json
Authorization: Bearer {jwt_token}
```

#### 1.2.2 统一响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": "2025-08-06T10:30:00Z",
  "requestId": "req_123456789"
}
```

#### 1.2.3 分页格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

## 2. 报价管理接口

### 2.1 创建报价

**接口地址**: `POST /quotations`  
**权限要求**: 需要登录  
**功能描述**: 创建新的报价

**请求参数**:
```json
{
  "title": "优质螺纹钢现货报价",
  "commodityName": "螺纹钢",
  "categoryId": 101,
  "deliveryLocation": "上海港",
  "brand": "宝钢",
  "specifications": "HRB400E, 直径12-32mm",
  "description": "质量保证，价格优惠",
  "priceType": "Fixed",
  "price": 4200.00,
  "instrumentRefId": null,
  "expiresAt": "2025-08-10T18:00:00Z",
  "status": "Draft"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "报价创建成功",
  "data": {
    "id": 12345,
    "title": "优质螺纹钢现货报价",
    "status": "Draft",
    "createdAt": "2025-08-06T10:30:00Z"
  }
}
```

### 2.2 更新报价

**接口地址**: `PUT /quotations/{id}`  
**权限要求**: 需要登录，且为报价创建者  
**功能描述**: 更新现有报价信息

**请求参数**: 同创建报价，所有字段可选

**响应示例**:
```json
{
  "code": 0,
  "message": "报价更新成功",
  "data": {
    "id": 12345,
    "updatedAt": "2025-08-06T11:00:00Z"
  }
}
```

### 2.3 获取我的报价列表

**接口地址**: `GET /quotations/my`  
**权限要求**: 需要登录  
**功能描述**: 获取当前用户创建的所有报价

**请求参数**:
```
?page=1&pageSize=20&status=Active&keyword=螺纹钢&sortBy=createdAt&sortOrder=desc
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 12345,
        "title": "优质螺纹钢现货报价",
        "commodityName": "螺纹钢",
        "price": 4200.00,
        "priceType": "Fixed",
        "status": "Active",
        "expiresAt": "2025-08-10T18:00:00Z",
        "viewCount": 128,
        "followCount": 12,
        "createdAt": "2025-08-06T10:30:00Z",
        "updatedAt": "2025-08-06T11:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 50,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "summary": {
      "totalQuotations": 50,
      "activeQuotations": 35,
      "totalFollowers": 156
    }
  }
}
```

### 2.4 删除报价

**接口地址**: `DELETE /quotations/{id}`  
**权限要求**: 需要登录，且为报价创建者  
**功能描述**: 删除指定报价（仅限草稿和已撤回状态）

**响应示例**:
```json
{
  "code": 0,
  "message": "报价删除成功"
}
```

---

## 3. 公开市场接口

### 3.1 获取公开报价列表

**接口地址**: `GET /quotations`  
**权限要求**: 无  
**功能描述**: 获取公开的有效报价列表

**请求参数**:
```
?page=1&pageSize=20&categoryIds=101,102&keyword=螺纹钢&priceType=Fixed&location=上海&sortBy=createdAt&sortOrder=desc
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 12345,
        "title": "优质螺纹钢现货报价",
        "commodityName": "螺纹钢",
        "categoryName": "黑色金属",
        "price": 4200.00,
        "priceType": "Fixed",
        "deliveryLocation": "上海港",
        "expiresAt": "2025-08-10T18:00:00Z",
        "viewCount": 128,
        "followCount": 12,
        "publisher": {
          "id": 1001,
          "username": "钢铁贸易商",
          "avatar": "https://example.com/avatar.jpg",
          "companyName": "上海钢铁有限公司"
        },
        "createdAt": "2025-08-06T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 200,
      "totalPages": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 3.2 获取报价详情

**接口地址**: `GET /quotations/{id}`  
**权限要求**: 无  
**功能描述**: 获取指定报价的详细信息

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 12345,
    "title": "优质螺纹钢现货报价",
    "commodityName": "螺纹钢",
    "categoryId": 101,
    "categoryName": "黑色金属",
    "deliveryLocation": "上海港",
    "brand": "宝钢",
    "specifications": "HRB400E, 直径12-32mm",
    "description": "质量保证，价格优惠",
    "priceType": "Fixed",
    "price": 4200.00,
    "expiresAt": "2025-08-10T18:00:00Z",
    "status": "Active",
    "viewCount": 128,
    "followCount": 12,
    "publisher": {
      "id": 1001,
      "username": "钢铁贸易商",
      "avatar": "https://example.com/avatar.jpg",
      "companyName": "上海钢铁有限公司",
      "location": "上海市",
      "phone": "138****8888",
      "isVerified": true
    },
    "isFollowed": false,
    "isOwner": false,
    "createdAt": "2025-08-06T10:30:00Z",
    "updatedAt": "2025-08-06T11:00:00Z"
  }
}
```

### 3.3 记录报价浏览

**接口地址**: `POST /quotations/{id}/view`  
**权限要求**: 无  
**功能描述**: 记录一次报价浏览行为

**响应示例**:
```json
{
  "code": 0,
  "message": "浏览记录成功"
}
```

---

## 4. 分类管理接口

### 4.1 获取商品分类

**接口地址**: `GET /categories`  
**权限要求**: 无  
**功能描述**: 获取所有商品分类及其版块信息

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "黑色金属",
      "sortOrder": 1,
      "categories": [
        {
          "id": 101,
          "name": "螺纹钢",
          "quotationCount": 45
        },
        {
          "id": 102,
          "name": "铁矿石",
          "quotationCount": 32
        }
      ]
    },
    {
      "id": 2,
      "name": "有色金属",
      "sortOrder": 2,
      "categories": [
        {
          "id": 201,
          "name": "电解铜",
          "quotationCount": 28
        }
      ]
    }
  ]
}
```

### 4.2 同步用户关注分类

**接口地址**: `POST /user/categories/sync`  
**权限要求**: 需要登录  
**功能描述**: 同步用户关注的分类到服务端

**请求参数**:
```json
{
  "categoryIds": [101, 102, 201],
  "timestamp": "2025-08-06T10:30:00Z"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "分类同步成功"
}
```

---

## 5. 关注系统接口

### 5.1 创建关注

**接口地址**: `POST /follows`  
**权限要求**: 需要登录  
**功能描述**: 关注用户或报价

**请求参数**:
```json
{
  "type": "User",
  "id": 1001
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "关注成功"
}
```

### 5.2 取消关注

**接口地址**: `DELETE /follows`  
**权限要求**: 需要登录  
**功能描述**: 取消关注用户或报价

**请求参数**:
```json
{
  "type": "User",
  "id": 1001
}
```

### 5.3 获取关注列表

**接口地址**: `GET /follows/my`  
**权限要求**: 需要登录  
**功能描述**: 获取我的关注列表

**请求参数**:
```
?type=Quotation&page=1&pageSize=20
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 12345,
        "title": "优质螺纹钢现货报价",
        "commodityName": "螺纹钢",
        "price": 4200.00,
        "status": "Active",
        "publisher": {
          "username": "钢铁贸易商",
          "companyName": "上海钢铁有限公司"
        },
        "followedAt": "2025-08-05T15:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 15,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 5.4 检查关注状态

**接口地址**: `GET /follows/status`  
**权限要求**: 需要登录  
**功能描述**: 检查是否已关注指定对象

**请求参数**:
```
?type=User&targetId=1001
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "isFollowed": true,
    "followedAt": "2025-08-05T15:30:00Z"
  }
}
```

---

## 6. 通知系统接口

### 6.1 获取通知列表

**接口地址**: `GET /notifications`  
**权限要求**: 需要登录  
**功能描述**: 获取用户的通知列表

**请求参数**:
```
?page=1&pageSize=20&isRead=false&type=QuotationUpdate
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1001,
        "type": "QuotationUpdate",
        "title": "报价更新提醒",
        "content": "您关注的"优质螺纹钢现货报价"已更新",
        "relatedQuotationId": 12345,
        "relatedUserId": null,
        "isRead": false,
        "createdAt": "2025-08-06T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 25,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    },
    "unreadCount": 8
  }
}
```

### 6.2 标记通知已读

**接口地址**: `POST /notifications/mark-read`  
**权限要求**: 需要登录  
**功能描述**: 标记一条或多条通知为已读

**请求参数**:
```json
{
  "ids": [1001, 1002, 1003]
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "标记成功",
  "data": {
    "markedCount": 3
  }
}
```

---

## 7. 统计分析接口

### 7.1 获取报价统计

**接口地址**: `GET /quotations/{id}/stats`  
**权限要求**: 需要登录，且为报价创建者  
**功能描述**: 获取指定报价的统计数据

**请求参数**:
```
?days=30
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "quotationId": 12345,
    "totalViews": 128,
    "totalFollows": 12,
    "dailyStats": [
      {
        "date": "2025-08-06",
        "viewCount": 15,
        "newFollowCount": 2
      },
      {
        "date": "2025-08-05",
        "viewCount": 12,
        "newFollowCount": 1
      }
    ]
  }
}
```

### 7.2 获取系统统计

**接口地址**: `GET /stats/system`  
**权限要求**: 无  
**功能描述**: 获取系统整体统计数据

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "todayStats": {
      "newQuotations": 45,
      "totalViews": 1250,
      "activeUsers": 320
    },
    "totalStats": {
      "totalQuotations": 5680,
      "totalUsers": 1200,
      "totalViews": 125000
    }
  }
}
```

---

## 8. 用户信息接口

### 8.1 获取用户公开档案

**接口地址**: `GET /users/{id}/profile`  
**权限要求**: 无  
**功能描述**: 获取用户的公开档案信息

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "钢铁贸易商",
    "avatar": "https://example.com/avatar.jpg",
    "companyName": "上海钢铁有限公司",
    "location": "上海市",
    "phone": "138****8888",
    "email": "contact@******.com",
    "isVerified": true,
    "quotationCount": 45,
    "followerCount": 156,
    "joinedAt": "2024-01-15T08:00:00Z"
  }
}
```

---

## 9. 错误码定义

### 9.1 通用错误码
```json
{
  "0": "成功",
  "1000": "参数错误",
  "1001": "缺少必要参数",
  "1002": "参数格式错误",
  "2000": "认证失败",
  "2001": "Token无效",
  "2002": "Token过期",
  "3000": "权限不足",
  "3001": "无访问权限",
  "4000": "资源不存在",
  "4001": "报价不存在",
  "4002": "用户不存在",
  "5000": "业务逻辑错误",
  "5001": "不能关注自己",
  "5002": "报价已过期",
  "5003": "重复操作",
  "9000": "系统错误",
  "9001": "数据库错误",
  "9002": "网络错误"
}
```

### 9.2 错误响应格式
```json
{
  "code": 1001,
  "message": "缺少必要参数: title",
  "data": null,
  "timestamp": "2025-08-06T10:30:00Z",
  "requestId": "req_123456789",
  "details": {
    "field": "title",
    "reason": "title字段不能为空"
  }
}
```

---

## 10. 接口安全

### 10.1 认证机制
- 使用JWT Token进行身份认证
- Token有效期为7天，支持刷新
- 敏感操作需要二次验证

### 10.2 限流策略
- 普通接口：每分钟100次请求
- 创建接口：每分钟10次请求
- 浏览记录：每分钟20次请求

### 10.3 数据安全
- 敏感信息脱敏处理
- 输入参数严格验证
- SQL注入防护
- XSS攻击防护
