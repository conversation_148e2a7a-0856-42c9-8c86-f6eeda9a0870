# 功能模块：13 - 合作伙伴管理 (Partnership Management)

---

## 1. 功能模块简介 (Summary)

本功能模块旨在为平台用户提供一个建立和管理业务伙伴关系（Partnership）的系统。用户可以互相发送邀请，建立正式的合作伙伴关系。这种关系一旦建立，将在平台的其他业务模块（如合同创建、交易发起）中作为权限和可见性的依据，确保交易仅在受信任的实体间进行。

- **核心流程**: 用户A向用户B发送邀请 -> 用户B接受/拒绝邀请 -> 双方建立/解除合作伙伴关系。
- **核心价值**:
    - **信任网络**: 将现实世界的商业信任关系数字化，构建一个可信的交易对手网络。
    - **风险控制**: 在创建合同等关键操作时，将交易对手限制在已建立的合作伙伴范围内，降低操作风险。
    - **效率提升**: 简化交易对手的选择过程，提高业务操作效率。

## 2. 数据定义 (Data Definition)

### 2.1. `dianjia_partners` - 合作伙伴关系表 (新增)

本表用于存储用户之间双向的合作伙伴关系记录。

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | `PK, AI` | 唯一ID |
| `user_id` | `BIGINT` | `NOT NULL, FK` | 关系发起方的用户ID |
| `partner_id` | `BIGINT` | `NOT NULL, FK` | 被邀请方的用户ID |
| `status` | `VARCHAR(20)` | `NOT NULL` | **关系状态** (见下方状态机) |
| `request_message` | `VARCHAR(255)` | `NULL` | 邀请时附带的请求信息 |
| `handled_at` | `TIMESTAMP` | `NULL` | 邀请被处理（接受/拒绝）的时间 |
| `created_at` | `TIMESTAMP` | | 创建时间 (邀请发起时间) |
| `updated_at` | `TIMESTAMP` | | 更新时间 |

### 2.2. 关系状态机 (Partnership Status Machine)

```mermaid
graph TD
    direction LR
    A[Pending] -- 接收方接受 / Accept --> B(Active);
    A -- 接收方拒绝 / Reject --> C(Rejected);
    B -- 任意一方解除关系 / Terminate --> D(Terminated);
    C -- 发起方删除记录 / Delete --> E[(Deleted)];
    D -- 任意一方删除记录 / Delete --> E;
```

- **`Pending` (待处理)**: 邀请已发送，等待接收方响应。
- **`Active` (已激活)**: 接收方已接受邀请，双方正式成为合作伙伴。
- **`Rejected` (已拒绝)**: 接收方已拒绝邀请。
- **`Terminated` (已终止)**: 曾是合作伙伴，但一方解除了关系。
