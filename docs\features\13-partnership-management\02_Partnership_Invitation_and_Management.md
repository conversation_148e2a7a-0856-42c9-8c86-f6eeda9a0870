# 02 - 合作伙伴邀请与管理 (Partnership Invitation and Management)

---

### 1. 功能设计 (Functional Design)

#### 1.1. 发起合作伙伴邀请
- **流程**:
  1. 用户A在合作伙伴管理页面，通过用户搜索功能找到潜在的合作伙伴用户B。
  2. 用户A点击“添加伙伴”，填写可选的邀请信息（如“我是XX公司的张三，希望与贵公司建立合作”），并发送邀请。
  3. 系统在 `dianjia_partners` 表中创建一条记录，`user_id` 为A，`partner_id` 为B，`status` 为 `Pending`。

#### 1.2. 处理收到的邀请
- **流程**:
  1. 用户B登录后，在合作伙伴管理页面的“收到的邀请”列表中看到用户A的邀请。
  2. 用户B可以选择 **[接受]** 或 **[拒绝]**。
     - **接受**: 该条记录的 `status` 更新为 `Active`。双方的合作伙伴列表中会互相显示对方。
     - **拒绝**: 该条记录的 `status` 更新为 `Rejected`。

#### 1.3. 管理现有合作伙伴
- **流程**:
  1. 在“我的伙伴”列表中，用户可以看到所有 `status` 为 `Active` 的合作伙伴。
  2. 对于列表中的每一个伙伴，用户都可以执行 **[解除关系]** 操作。
  3. 解除关系后，该记录的 `status` 更新为 `Terminated`。双方的合作伙伴列表中将不再显示对方。

#### 1.4. 权限与数据隔离
- **核心影响**: 本功能将直接影响 **[功能03 - 用户选择器组件](../00-base-features/03_User_Selector_Component.md)** 的数据范围。
- **修改逻辑**:
  - 原有的 `/api/v1/user/getSelectableList` 接口需要进行改造。
  - 当该接口被特定业务场景（如创建合同）调用时，需要增加一个参数，如 `context=partner`。
  - 当检测到此上下文时，接口的查询逻辑应变更为：**仅返回当前登录用户的所有 `Active` 状态的合作伙伴列表**。
  - 这确保了用户在发起交易时，只能从自己已经建立信任的伙伴中进行选择。

### 2. 接口定义 (API Definition)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 请求体说明 | 角色 |
| :--- | :--- | :--- | :--- | :--- |
| **发送合作伙伴邀请** | `POST` | `/api/v1/dianjia/partners/invite` | `{"partner_id": 123, "message": "..."}` | 已登录用户 |
| **接受邀请** | `POST` | `/api/v1/dianjia/partners/invitations/{id}/accept` | (无) | 邀请接收方 |
| **拒绝邀请** | `POST` | `/api/v1/dianjia/partners/invitations/{id}/reject` | (无) | 邀请接收方 |
| **解除合作伙伴关系** | `DELETE` | `/api/v1/dianjia/partners/{id}` | (无) | 关系双方任意一方 |
| **获取合作伙伴列表** | `GET` | `/api/v1/dianjia/partners` | `?status=active` | 已登录用户 |
| **获取邀请列表** | `GET` | `/api/v1/dianjia/partners/invitations` | `?direction=received/sent` | 已登录用户 |
| **获取可选择的用户(伙伴)**| `GET` | `/api/v1/user/getSelectableList` | `?context=partner` | 已登录用户 |

### 3. 相关页面 (Related Pages)

- **合作伙伴管理页**: `app/src/pages/profile/partners.vue`
  - 此页面包含多个Tab或区域，分别用于显示：
    - 我的伙伴 (`Active` 状态)
    - 收到的邀请 (`Pending` 状态，且 `partner_id` 为自己)
    - 发出的邀请 (`Pending` 状态，且 `user_id` 为自己)
- **用户选择器组件**: `app/src/components/UserSelector.vue`
  - 需要根据调用上下文，传递 `context=partner` 参数。

### 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-PARTNER-001` | **成功发送邀请** | 1. 用户A搜索到用户B并发起邀请。 | 1. A的“发出的邀请”列表出现B。<br>2. B的“收到的邀请”列表出现A。 |
| `TC-PARTNER-002` | **接受邀请** | 1. 用户B在邀请列表中接受A的邀请。 | 1. A和B互相出现在对方的“我的伙伴”列表中。<br>2. 邀请记录从待处理列表中消失。 |
| `TC-PARTNER-003` | **拒绝邀请** | 1. 用户B在邀请列表中拒绝A的邀请。 | 1. A和B的伙伴关系状态变为`Rejected`。<br>2. 邀请记录从待处理列表中消失。 |
| `TC-PARTNER-004` | **解除关系** | 1. A和B已是伙伴。<br>2. A在伙伴列表中将B解除关系。 | 1. A和B不再出现在对方的伙伴列表中。<br>2. 关系状态变为`Terminated`。 |
| `TC-PARTNER-005` | **用户选择器隔离** | 1. A和B是伙伴，A和C不是。<br>2. A在创建合同时，打开用户选择器。 | 选择器列表中只应出现用户B，不应出现用户C。 |
| `TC-PARTNER-006` | **重复邀请** | 1. A已向B发送邀请（Pending状态）。<br>2. A再次尝试向B发送邀请。 | 系统提示“已存在待处理的邀请，请勿重复发送”。 |
