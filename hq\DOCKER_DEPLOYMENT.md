# Docker 部署指南

## 概述

本文档描述如何使用 Docker 部署 hq 量化交易行情引擎。

## 技术栈

- **基础镜像**: Python 3.13-slim
- **包管理器**: UV (极速 Python 包管理器)
- **依赖管理**: pyproject.toml + uv.lock
- **启动方式**: `uv run main.py config.ini --schedule`

## 文件说明

### Docker 相关文件

- `Dockerfile` - 主要的 Docker 镜像构建文件
- `docker-compose.yml` - 完整的服务编排文件
- `docker-entrypoint.sh` - 容器启动脚本
- `.dockerignore` - Docker 构建忽略文件
- `docker_config.ini` - Docker 环境专用配置

## 快速开始

### 1. 单独构建和运行

```bash
# 构建镜像
docker build -t hq-engine .

# 运行容器
docker run -d \
  --name hq-engine \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/cache:/app/cache \
  hq-engine
```

### 2. 使用 Docker Compose（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f hq-engine

# 停止服务
docker-compose down
```

## 配置说明

### 环境变量

- `PYTHONUNBUFFERED=1` - 禁用 Python 输出缓冲
- `LANG=zh_CN.GB18030` - 中文语言环境
- `WAIT_FOR_SERVICES=true` - 等待依赖服务启动
- `HQ_REDIS_HOST=host.docker.internal` - Redis服务主机地址
- `HQ_REDIS_PASSWORD=123456` - Redis服务密码

### 数据卷挂载

- `./logs:/app/logs` - 日志文件持久化
- `./cache:/app/cache` - 缓存文件持久化
- `./custom_config.ini:/app/docker_config.ini` - 自定义配置（可选）

### 网络配置

默认使用桥接网络 `hq-network`，容器通过 `host.docker.internal` 访问宿主机服务：
- Redis: `host.docker.internal:6379`
- MySQL: `host.docker.internal:3306` (如果需要)

容器配置了额外的主机映射以确保能够访问宿主机服务。

## 服务依赖

### 外部服务依赖

应用依赖宿主机上运行的服务：

#### Redis
- 主机: 宿主机 (通过 `host.docker.internal` 访问)
- 端口: `6379`
- 密码: `123456`

#### MySQL (可选)
- 主机: 宿主机 (通过 `host.docker.internal` 访问)
- 端口: `3306`
- 数据库: `dianhaojia`

### 环境变量配置

通过环境变量可以覆盖配置文件中的设置：
- `HQ_REDIS_HOST` - Redis主机地址
- `HQ_REDIS_PASSWORD` - Redis密码
- `HQ_DATABASE_HOST` - MySQL主机地址 (如果使用)
- `HQ_DATABASE_PASSWORD` - MySQL密码 (如果使用)

## 启动模式

### 调度模式（默认）
```bash
docker run hq-engine docker_config.ini --schedule
```

### 直接模式
```bash
docker run hq-engine docker_config.ini
```

## 健康检查

容器包含内置健康检查：
- 检查间隔: 30秒
- 超时时间: 10秒
- 启动等待: 5秒
- 重试次数: 3次

## 日志管理

### 查看实时日志
```bash
# Docker Compose
docker-compose logs -f hq-engine

# 单独容器
docker logs -f hq-engine
```

### 日志文件位置
- 容器内: `/app/logs/`
- 宿主机: `./logs/` (通过卷挂载)

## 故障排除

### 1. 容器启动失败
```bash
# 检查容器状态
docker ps -a

# 查看启动日志
docker logs hq-engine

# 进入容器调试
docker exec -it hq-engine bash
```

### 2. 依赖服务连接失败
```bash
# 检查网络连接
docker exec hq-engine nc -z redis 6379
docker exec hq-engine nc -z mysql 3306

# 检查服务状态
docker-compose ps
```

### 3. 配置问题
```bash
# 检查配置文件
docker exec hq-engine cat docker_config.ini

# 验证环境变量
docker exec hq-engine env | grep -E "(LANG|PYTHON)"
```

## 性能优化

### 1. 资源限制
```yaml
services:
  hq-engine:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

### 2. 镜像优化
- 使用多阶段构建减小镜像大小
- 利用 Docker 层缓存加速构建
- 定期清理未使用的镜像和容器

## 安全建议

1. **配置文件安全**
   - 不要在镜像中包含敏感配置
   - 使用 Docker secrets 或环境变量传递敏感信息

2. **网络安全**
   - 限制容器间不必要的网络访问
   - 使用防火墙规则保护暴露的端口

3. **镜像安全**
   - 定期更新基础镜像
   - 扫描镜像漏洞
   - 使用非 root 用户运行应用（如需要）

## 监控和维护

### 1. 容器监控
```bash
# 查看资源使用情况
docker stats hq-engine

# 查看容器详细信息
docker inspect hq-engine
```

### 2. 数据备份
```bash
# 备份 MySQL 数据
docker exec mysql mysqldump -u root -p dianhaojia > backup.sql

# 备份 Redis 数据
docker exec redis redis-cli BGSAVE
```

### 3. 更新部署
```bash
# 重新构建并更新
docker-compose build --no-cache
docker-compose up -d
```
