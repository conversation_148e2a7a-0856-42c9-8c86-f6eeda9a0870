version: '3.8'

services:
  # 量化交易行情引擎
  hq-engine:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hq-engine
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - LANG=zh_CN.GB18030
      - LC_ALL=zh_CN.GB18030
      - HQ_REDIS_HOST=host.docker.internal
      - HQ_REDIS_PASSWORD=123456
    volumes:
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载缓存目录
      - ./cache:/app/cache
      # 如果需要自定义配置，可以挂载配置文件
      # - ./custom_config.ini:/app/docker_config.ini
    networks:
      - hq-network
    # 添加额外的主机映射，确保能访问宿主机服务
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  hq-network:
    driver: bridge
