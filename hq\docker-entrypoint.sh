#!/bin/bash

# Docker 容器启动脚本
# 用于 hq 量化交易行情引擎

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待服务就绪
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log_info "等待 $service_name 服务就绪 ($host:$port)..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_info "$service_name 服务已就绪"
            return 0
        fi
        sleep 1
    done
    
    log_error "$service_name 服务在 $timeout 秒内未就绪"
    return 1
}

# 检查配置文件
check_config() {
    local config_file=${1:-"docker_config.ini"}
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件 $config_file 不存在"
        exit 1
    fi
    
    log_info "使用配置文件: $config_file"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    mkdir -p logs cache
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查 Python 环境
    if ! uv run python -c "import sys; print(f'Python {sys.version}')"; then
        log_error "Python 环境检查失败"
        return 1
    fi
    
    # 检查关键依赖
    if ! uv run python -c "import vnpy, redis, aiohttp"; then
        log_error "关键依赖检查失败"
        return 1
    fi
    
    log_info "健康检查通过"
    return 0
}

# 主函数
main() {
    log_info "启动 hq 量化交易行情引擎..."
    
    # 创建目录
    create_directories
    
    # 检查配置文件
    local config_file=${1:-"docker_config.ini"}
    check_config "$config_file"
    
    # 健康检查
    if ! health_check; then
        log_error "健康检查失败，退出"
        exit 1
    fi
    
    # 等待依赖服务（如果在 docker-compose 环境中）
    if [ "$WAIT_FOR_SERVICES" = "true" ]; then
        # 从环境变量获取Redis主机，默认为宿主机
        redis_host=${HQ_REDIS_HOST:-"host.docker.internal"}
        wait_for_service "$redis_host" "6379" "Redis" 60

        # 如果配置了MySQL主机，也等待MySQL服务
        if [ -n "$HQ_DATABASE_HOST" ]; then
            mysql_host=${HQ_DATABASE_HOST:-"host.docker.internal"}
            wait_for_service "$mysql_host" "3306" "MySQL" 60
        fi
    fi
    
    # 启动应用
    log_info "启动应用程序..."
    
    # 根据参数决定启动模式
    if [ "$2" = "--schedule" ] || [ "$2" = "schedule" ]; then
        log_info "使用调度模式启动"
        exec uv run main.py "$config_file" --schedule
    else
        log_info "使用直接模式启动"
        exec uv run main.py "$config_file"
    fi
}

# 信号处理
cleanup() {
    log_info "收到停止信号，正在清理..."
    # 这里可以添加清理逻辑
    exit 0
}

trap cleanup SIGTERM SIGINT

# 如果脚本被直接调用
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
