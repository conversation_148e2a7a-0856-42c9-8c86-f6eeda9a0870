"""
配置管理模块

支持从 config.ini 文件读取配置，并允许通过环境变量覆盖配置值。
环境变量命名规范：HQ_{SECTION}_{KEY}，全部大写
例如：HQ_DATABASE_HOST, HQ_REDIS_PORT 等
"""
import configparser
import os
from pathlib import Path
from typing import Optional, Dict, Any, Union

# 用于存储配置的私有全局变量
_config: Optional[Dict[str, Any]] = None
_initialized: bool = False
_env_overrides: Dict[str, str] = {}  # 记录哪些配置来自环境变量

def _get_default_config_path() -> str:
    """获取默认配置文件路径 (项目根目录下的 config.ini)"""
    current_dir = Path(__file__).parent.parent
    return str(current_dir / "config.ini")

def _parse_config(parser: configparser.ConfigParser) -> Dict[str, Any]:
    """将 ConfigParser 对象解析为字典"""
    config_dict = {}
    for section in parser.sections():
        config_dict[section] = {}
        for key, value in parser.items(section):
            config_dict[section][key] = value
    return config_dict

def _convert_env_value(env_value: str, original_value: str) -> Union[str, int, float, bool]:
    """
    将环境变量值转换为适当的类型

    根据原始配置值的类型来推断目标类型
    """
    if original_value is None:
        return env_value

    # 尝试转换为原始值的类型
    if original_value.isdigit():
        try:
            return int(env_value)
        except ValueError:
            return env_value

    # 尝试转换为浮点数
    try:
        float(original_value)
        return float(env_value)
    except ValueError:
        pass

    # 布尔值转换
    if original_value.lower() in ('true', 'false', 'yes', 'no', '1', '0'):
        return env_value.lower() in ('true', 'yes', '1')

    # 默认返回字符串
    return env_value

def _apply_env_overrides(config_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用环境变量覆盖配置文件中的值

    环境变量命名规范：HQ_{SECTION}_{KEY}，全部大写
    例如：HQ_DATABASE_HOST, HQ_REDIS_PORT 等
    """
    global _env_overrides
    _env_overrides.clear()

    for section_name, section_config in config_dict.items():
        for key, value in section_config.items():
            # 构造环境变量名：HQ_SECTION_KEY
            env_var_name = f"HQ_{section_name.upper()}_{key.upper()}"
            env_value = os.getenv(env_var_name)

            if env_value is not None:
                # 记录环境变量覆盖
                _env_overrides[f"{section_name}.{key}"] = env_var_name
                # 转换类型并覆盖配置值
                config_dict[section_name][key] = _convert_env_value(env_value, value)
                print(f"配置 [{section_name}].{key} 被环境变量 {env_var_name} 覆盖")

    return config_dict

def init_config(config_path: Optional[str] = None):
    """
    初始化配置系统。

    从指定的 .ini 文件读取配置并加载到内存中，然后应用环境变量覆盖。
    此函数应在应用程序启动时调用一次。
    """
    global _config, _initialized
    if _initialized:
        print("配置已经初始化，跳过。")
        return

    path = config_path or _get_default_config_path()
    if not os.path.exists(path):
        # 如果配置文件不存在，创建一个空的 _config，并标记为已初始化
        print(f"警告: 配置文件不存在于 {path}。将使用空配置。")
        _config = {}
        _initialized = True
        return

    try:
        parser = configparser.ConfigParser()
        parser.read(path, encoding='utf-8')
        _config = _parse_config(parser)

        # 应用环境变量覆盖
        _config = _apply_env_overrides(_config)

        _initialized = True
        print(f"配置系统已从 {path} 初始化")

        # 显示环境变量覆盖信息
        if _env_overrides:
            print(f"共有 {len(_env_overrides)} 个配置项被环境变量覆盖")

    except Exception as e:
        print(f"错误: 初始化配置失败: {e}")
        _config = {} # 即使失败也设置为空字典，避免None
        _initialized = True # 标记为已初始化以防止重试

def get_config() -> Dict[str, Any]:
    """
    获取已加载的配置。

    如果配置未初始化，将引发 RuntimeError。
    """
    if not _initialized or _config is None:
        raise RuntimeError("配置系统尚未初始化。请在程序启动时调用 init_config()")
    return _config

def get_section(name: str) -> Dict[str, Any]:
    """
    辅助函数，用于从配置中获取特定部分。

    如果部分不存在，则返回一个空字典。
    """
    config = get_config()
    return config.get(name, {})

def get_config_value(section: str, key: str, default: Any = None) -> Any:
    """
    获取单个配置值的辅助函数。

    Args:
        section: 配置段名称
        key: 配置键名称
        default: 如果配置不存在时的默认值

    Returns:
        配置值，如果不存在则返回默认值
    """
    section_config = get_section(section)
    return section_config.get(key, default)

def get_env_overrides() -> Dict[str, str]:
    """
    获取当前被环境变量覆盖的配置项信息。

    Returns:
        字典，键为 "section.key" 格式，值为对应的环境变量名
    """
    return _env_overrides.copy()

def print_config_sources():
    """
    打印配置来源信息，用于调试。
    """
    if not _initialized:
        print("配置系统尚未初始化")
        return

    config = get_config()
    print("\n=== 配置来源信息 ===")

    for section_name, section_config in config.items():
        print(f"\n[{section_name}]")
        for key, value in section_config.items():
            config_key = f"{section_name}.{key}"
            if config_key in _env_overrides:
                env_var = _env_overrides[config_key]
                print(f"  {key} = {value} (来自环境变量 {env_var})")
            else:
                print(f"  {key} = {value} (来自配置文件)")

    print("==================\n")
